/*
 * Function: ?UpdateLogFileName@CAsyncLogInfo@@QEAAXXZ
 * Address: 0x1403BD0F0
 */

void __usercall CAsyncLogInfo::UpdateLogFileName(CAsyncLogInfo *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp-20h] [bp-2998h]@1
  char *v6; // [sp+0h] [bp-2978h]@7
  char szTime; // [sp+20h] [bp-2958h]@6
  char v8; // [sp+21h] [bp-2957h]@6
  char DstBuf; // [sp+140h] [bp-2838h]@7
  char v10; // [sp+141h] [bp-2837h]@7
  rsize_t SizeInBytes; // [sp+2948h] [bp-30h]@7
  int v12; // [sp+2950h] [bp-28h]@7
  unsigned __int64 v13; // [sp+2960h] [bp-18h]@4
  CAsyncLogInfo *v14; // [sp+2980h] [bp+8h]@1

  v14 = this;
  v2 = alloca(a2);
  v3 = &v5;
  for ( i = 2660i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v14->m_pkTimer )
  {
    if ( CMyTimer::CountingTimer(v14->m_pkTimer) )
    {
      szTime = 0;
      memset(&v8, 0, 0xFFui64);
      if ( GetDateTimeStr(&szTime) )
      {
        DstBuf = 0;
        memset(&v10, 0, 0x27FFui64);
        SizeInBytes = strlen_0(v14->m_szLogFileName) + 1;
        v6 = &szTime;
        v12 = sprintf_s(&DstBuf, 0x2800ui64, "%s_%s.log", v14->m_szLogDirPath);
        if ( v12 > 0 )
        {
          CNetCriticalSection::Lock(&v14->m_csLock);
          strcpy_s(v14->m_szLogFileName, SizeInBytes, &DstBuf);
          CNetCriticalSection::Unlock(&v14->m_csLock);
        }
      }
    }
  }
}
