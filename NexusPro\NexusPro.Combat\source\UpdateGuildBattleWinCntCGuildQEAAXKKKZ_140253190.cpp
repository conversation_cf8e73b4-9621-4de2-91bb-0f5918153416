/*
 * Function: ?UpdateGuildBattleWinCnt@CGuild@@QEAAXKKK@Z
 * Address: 0x140253190
 */

void __fastcall CGuild::UpdateGuildBattleWinCnt(CGuild *this, unsigned int dwTotWin, unsigned int dwTotDraw, unsigned int dwTotLose)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CGuild *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7->m_dwGuildBattleTotWin = dwTotWin;
  v7->m_dwGuildBattleTotDraw = dwTotDraw;
  v7->m_dwGuildBattleTotLose = dwTotLose;
  CGuild::MakeQueryInfoPacket(v7);
  CGuild::MakeDownMemberPacket(v7);
  v7->m_byMoneyOutputKind = 0;
  CGuild::SendMsg_GuildInfoUpdateInform(v7);
}
