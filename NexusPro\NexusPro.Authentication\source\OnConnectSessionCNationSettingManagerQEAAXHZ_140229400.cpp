﻿/*
 * Function: ?OnConnectSession@CNationSettingManager@@XH@Z
 * Address: 0x140229400
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CNationSettingManager::OnConnectSession(CNationSettingManager *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  INationGameGuardSystem *v5; // [sp+20h] [bp-18h]@5
  CNationSettingManager *v6; // [sp+40h] [bp+8h]@1
  int v7; // [sp+48h] [bp+10h]@1

  v7 = n;
  v6 = this;
  v2 = &v4;
  for ( i = 12LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CNationSettingData::GetGameGuardSystem(v6->m_pData) )
  {
    v5 = CNationSettingData::GetGameGuardSystem(v6->m_pData);
    (*(void ( **)(INationGameGuardSystem *, _QWORD))&v5->vfptr->gap8[0])(v5, (unsigned int)v7);
  }
}


