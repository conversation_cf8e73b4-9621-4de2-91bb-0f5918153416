﻿/*
 * Function: ?SendMsg_GuildMemberLogin@CGuild@@XKGG@Z
 * Address: 0x1402570F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CGuild::SendMsg_GuildMemberLogin(CGuild *this, unsigned int dwSerial, unsigned __int16 wMapCode, unsigned __int16 wRegionIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  unsigned __int16 v8; // [sp+38h] [bp-40h]@4
  char v9; // [sp+3Ah] [bp-3Eh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  CGuild *v13; // [sp+80h] [bp+8h]@1
  unsigned int v14; // [sp+88h] [bp+10h]@1

  v14 = dwSerial;
  v13 = this;
  v4 = &v6;
  for ( i = 28LL; i; --i )
  {
    *(DWORD *)v4 = 0xCCCCCCCC;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  *(DWORD *)szMsg = dwSerial;
  v8 = wMapCode;
  v9 = wRegionIndex;
  pbyType = 27;
  v11 = 44;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v13->m_MemberData[j])
      && v13->m_MemberData[j].pPlayer
      && v13->m_MemberData[j].dwSerial != v14 )
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 7u);
    }
  }
}


