#!/usr/bin/env python3
"""
Simple build test script for NexusPro modules
"""

import subprocess
import sys
from pathlib import Path

def test_module_build(module_name: str) -> bool:
    """Test if a specific module builds successfully"""
    project_path = Path(f"NexusPro/{module_name}/{module_name}.vcxproj")
    
    if not project_path.exists():
        print(f"❌ Project file not found: {project_path}")
        return False
    
    msbuild_path = r"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    
    if not Path(msbuild_path).exists():
        print(f"❌ MSBuild not found at: {msbuild_path}")
        return False
    
    print(f"🔨 Building {module_name}...")
    
    try:
        result = subprocess.run([
            msbuild_path,
            str(project_path),
            "/p:Configuration=Debug",
            "/p:Platform=x64",
            "/v:minimal",
            "/nologo"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {module_name} BUILD SUCCESS!")
            return True
        else:
            print(f"❌ {module_name} build failed:")
            # Show error output
            if result.stderr:
                error_lines = result.stderr.split('\n')
                for line in error_lines[-20:]:  # Last 20 lines
                    if line.strip():
                        print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {module_name} build timeout (5 minutes)")
        return False
    except Exception as e:
        print(f"❌ {module_name} build test failed: {e}")
        return False

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python test_build.py <module_name>")
        print("Example: python test_build.py NexusPro.Core")
        print("         python test_build.py NexusPro.Authentication")
        return
    
    module_name = sys.argv[1]
    
    print("=" * 50)
    print(f"NexusPro Build Test - {module_name}")
    print("=" * 50)
    
    success = test_module_build(module_name)
    
    if success:
        print(f"\n🎉 {module_name} builds successfully!")
    else:
        print(f"\n💥 {module_name} has build issues")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
