﻿/*
 * Function: j_??$make_pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@std@@YA?AU?$pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@0@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@Z
 * Address: 0x1400120E4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *__fastcall std::make_pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>(std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *result, ASYNC_LOG_TYPE _Val1, CAsyncLogInfo *_Val2)
{
  return std::make_pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>(result, _Val1, _Val2);
}

