﻿/*
 * Function: j_??0?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@@AEBV01@@Z
 * Address: 0x14000EF34
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *this, std::allocator<std::pair<int const ,CAsyncLogInfo *> > *__formal)
{
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(this, __formal);
}

