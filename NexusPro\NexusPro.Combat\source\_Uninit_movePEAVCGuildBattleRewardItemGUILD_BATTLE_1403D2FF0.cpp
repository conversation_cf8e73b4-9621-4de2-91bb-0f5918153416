/*
 * Function: ??$_Uninit_move@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@U_Undefined_move_tag@4@@std@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403D2FF0
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::_Uninit_move<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>,std::_Undefined_move_tag>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleRewardItem *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  return stdext::unchecked_uninitialized_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(
           _Firsta,
           _Last,
           _Dest,
           _Al);
}
