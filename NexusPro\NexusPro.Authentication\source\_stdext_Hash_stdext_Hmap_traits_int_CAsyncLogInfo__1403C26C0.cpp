/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int_CAsyncLogInfo_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64____0___::insert_::_1_::dtor$14
 * Address: 0x1403C26C0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int_CAsyncLogInfo_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64____0___::insert_::_1_::dtor_14(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 612) & 1 )
  {
    *(_DWORD *)(a2 + 612) &= 0xFFFFFFFE;
    std::pair<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,bool>::~pair<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,bool>(*(std::pair<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0>,bool> **)(a2 + 856));
  }
}
