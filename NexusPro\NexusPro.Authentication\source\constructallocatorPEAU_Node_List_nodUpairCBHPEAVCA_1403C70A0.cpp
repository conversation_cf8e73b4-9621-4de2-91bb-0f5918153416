﻿/*
 * Function: ?construct@?$allocator@PEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@XPEAPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@AEBQEAU342@@Z
 * Address: 0x1403C70A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *>::construct(std::allocator<std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *> *this, std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node **_Ptr, std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *const *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *,std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *>(
    _Ptr,
    _Val);
}


