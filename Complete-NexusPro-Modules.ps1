param(
    [string]$StartModule = "Authentication",
    [switch]$DryRun = $false,
    [switch]$BuildTest = $true,
    [int]$MaxFilesPerBatch = 50
)

Write-Host "NexusPro Systematic Module Completion Script" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Define module processing order based on dependency complexity
$ModuleOrder = @(
    "NexusPro.Authentication",
    "NexusPro.Network", 
    "NexusPro.Database",
    "NexusPro.Security",
    "NexusPro.Player",
    "NexusPro.Items",
    "NexusPro.Economy",
    "NexusPro.Combat",
    "NexusPro.World",
    "NexusPro.System"
    # Core is already working
)

# Enhanced fix patterns based on our analysis
$FixPatterns = @{
    # Type fixes
    "_DWORD" = "DWORD"
    "_BYTE" = "BYTE" 
    "_WORD" = "WORD"
    "_QWORD" = "QWORD"
    
    # Calling convention markers
    "QEAA" = ""
    "UEAA" = ""
    "MEAA" = ""
    "AEAA" = ""
    "YEAA" = ""
    "PEAA" = ""
    
    # Hex constants
    "-858993460" = "0xCCCCCCCC"
    
    # Common patterns
    "operator delete\[\]" = "delete[]"
    "__fastcall" = ""
}

$IncludeTemplate = @"
#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

"@

function Test-ModuleBuild {
    param([string]$ModuleName)
    
    Write-Host "Testing build for module: $ModuleName" -ForegroundColor Yellow
    
    $projectPath = "NexusPro\$ModuleName\$ModuleName.vcxproj"
    if (!(Test-Path $projectPath)) {
        Write-Host "  Project file not found: $projectPath" -ForegroundColor Red
        return $false
    }
    
    $msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    
    try {
        $result = & $msbuildPath $projectPath /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "  BUILD SUCCESS!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  Build failed with errors:" -ForegroundColor Red
            $result | Select-Object -Last 10 | ForEach-Object { Write-Host "    $_" -ForegroundColor Red }
            return $false
        }
    } catch {
        Write-Host "  Build test failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Process-SourceFile {
    param([string]$FilePath, [string]$ModuleName)
    
    $content = Get-Content $FilePath -Raw -ErrorAction SilentlyContinue
    if (!$content) { return $false }
    
    $originalContent = $content
    $changesMade = $false
    
    # Add includes if missing
    if ($content -notmatch "#include.*NexusProCommon\.h") {
        $lines = Get-Content $FilePath
        $insertIndex = 0
        
        # Find insertion point after comments
        for ($i = 0; $i -lt $lines.Count; $i++) {
            $line = $lines[$i].Trim()
            if ($line -and $line -ne "*/" -and !$line.StartsWith("/*") -and !$line.StartsWith("//") -and !$line.StartsWith("*")) {
                $insertIndex = $i
                break
            }
        }
        
        if (!$DryRun) {
            $newLines = $lines[0..($insertIndex-1)] + $IncludeTemplate.Split("`n") + $lines[$insertIndex..($lines.Count-1)]
            $newLines | Set-Content $FilePath -Encoding UTF8
        }
        $changesMade = $true
    }
    
    # Apply pattern fixes
    $content = Get-Content $FilePath -Raw
    foreach ($pattern in $FixPatterns.Keys) {
        $replacement = $FixPatterns[$pattern]
        if ($content -match [regex]::Escape($pattern)) {
            if (!$DryRun) {
                $content = $content -replace [regex]::Escape($pattern), $replacement
            }
            $changesMade = $true
        }
    }
    
    # Fix numeric literals
    if ($content -match "(\d+)i64") {
        if (!$DryRun) {
            $content = $content -replace "(\d+)i64", '$1LL'
        }
        $changesMade = $true
    }
    
    # Simplify complex function signatures
    if ($content -match 'void __fastcall (\w+)::(\w+)\(\1 \*this\)') {
        if (!$DryRun) {
            $content = $content -replace 'void __fastcall (\w+)::(\w+)\(\1 \*this\)', '$1::$2()'
        }
        $changesMade = $true
    }
    
    # Save changes
    if ($changesMade -and !$DryRun) {
        $content | Set-Content $FilePath -Encoding UTF8
    }
    
    return $changesMade
}

function Process-Module {
    param([string]$ModuleName)
    
    Write-Host ""
    Write-Host "=== PROCESSING MODULE: $ModuleName ===" -ForegroundColor Cyan
    
    $modulePath = "NexusPro\$ModuleName"
    if (!(Test-Path $modulePath)) {
        Write-Host "Module path not found: $modulePath" -ForegroundColor Red
        return $false
    }
    
    # Find source files
    $sourceFiles = Get-ChildItem -Path "$modulePath\source" -Filter "*.cpp" -ErrorAction SilentlyContinue
    if (!$sourceFiles) {
        Write-Host "No source files found in $modulePath\source" -ForegroundColor Yellow
        return $true
    }
    
    Write-Host "Found $($sourceFiles.Count) source files to process" -ForegroundColor White
    
    $processedFiles = 0
    $modifiedFiles = 0
    $batchCount = 0
    
    # Process files in batches
    for ($i = 0; $i -lt $sourceFiles.Count; $i += $MaxFilesPerBatch) {
        $batchCount++
        $batch = $sourceFiles[$i..([Math]::Min($i + $MaxFilesPerBatch - 1, $sourceFiles.Count - 1))]
        
        Write-Host "Processing batch $batchCount (files $($i+1)-$($i+$batch.Count))..." -ForegroundColor Yellow
        
        foreach ($file in $batch) {
            if (Process-SourceFile -FilePath $file.FullName -ModuleName $ModuleName) {
                $modifiedFiles++
            }
            $processedFiles++
            
            # Progress indicator
            if ($processedFiles % 25 -eq 0) {
                Write-Host "  Progress: $processedFiles/$($sourceFiles.Count) files" -ForegroundColor Gray
            }
        }
        
        # Test build after each batch if requested
        if ($BuildTest -and !$DryRun -and $modifiedFiles -gt 0) {
            Write-Host "Testing build after batch $batchCount..." -ForegroundColor Yellow
            if (Test-ModuleBuild -ModuleName $ModuleName) {
                Write-Host "  Batch ${batchCount}: Build successful!" -ForegroundColor Green
            } else {
                Write-Host "  Batch ${batchCount}: Build failed - stopping module processing" -ForegroundColor Red
                break
            }
        }
    }
    
    Write-Host "Module $ModuleName completed:" -ForegroundColor Green
    Write-Host "  Files processed: $processedFiles" -ForegroundColor White
    Write-Host "  Files modified: $modifiedFiles" -ForegroundColor White
    
    # Final build test
    if ($BuildTest -and !$DryRun) {
        Write-Host "Final build test for $ModuleName..." -ForegroundColor Yellow
        return Test-ModuleBuild -ModuleName $ModuleName
    }
    
    return $true
}

# Main execution
try {
    $startTime = Get-Date
    
    if ($DryRun) {
        Write-Host "DRY RUN MODE - No files will be modified" -ForegroundColor Yellow
    }
    
    # Find starting point
    $startIndex = $ModuleOrder.IndexOf("NexusPro.$StartModule")
    if ($startIndex -eq -1) {
        Write-Host "Invalid start module: $StartModule" -ForegroundColor Red
        exit 1
    }
    
    $modulesToProcess = $ModuleOrder[$startIndex..($ModuleOrder.Count-1)]
    Write-Host "Processing $($modulesToProcess.Count) modules starting from $StartModule" -ForegroundColor White
    Write-Host ""
    
    $successfulModules = @()
    $failedModules = @()
    
    foreach ($module in $modulesToProcess) {
        if (Process-Module -ModuleName $module) {
            $successfulModules += $module
            Write-Host "✅ $module completed successfully" -ForegroundColor Green
        } else {
            $failedModules += $module
            Write-Host "❌ $module failed" -ForegroundColor Red
            
            # Ask user if they want to continue
            if (!$DryRun) {
                $continue = Read-Host "Continue with next module? (y/n)"
                if ($continue -ne "y") {
                    break
                }
            }
        }
    }
    
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Write-Host ""
    Write-Host "=== COMPLETION SUMMARY ===" -ForegroundColor Green
    Write-Host "Successful modules: $($successfulModules.Count)" -ForegroundColor Green
    Write-Host "Failed modules: $($failedModules.Count)" -ForegroundColor Red
    Write-Host "Duration: $($duration.TotalMinutes.ToString('F1')) minutes" -ForegroundColor White
    
    if ($successfulModules.Count -gt 0) {
        Write-Host ""
        Write-Host "✅ Successful modules:" -ForegroundColor Green
        $successfulModules | ForEach-Object { Write-Host "  $_" -ForegroundColor Green }
    }
    
    if ($failedModules.Count -gt 0) {
        Write-Host ""
        Write-Host "❌ Failed modules:" -ForegroundColor Red
        $failedModules | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
    }
    
} catch {
    Write-Error "Error during processing: $($_.Exception.Message)"
    exit 1
}
