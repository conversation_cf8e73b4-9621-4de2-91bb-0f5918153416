﻿/*
 * Function: ?CallFunc_RFOnline_Auth@CEnglandBillingMgr@@HAEAU_param_cash_select@@H@Z
 * Address: 0x1403198F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

signed __int64  CEnglandBillingMgr::CallFunc_RFOnline_Auth(CEnglandBillingMgr *this, _param_cash_select *rParam, int nIdx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  CEngNetworkBillEX *v6; // rax@4
  signed __int64 result; // rax@5
  RECV_DATA *v8; // rax@7
  __int64 v9; // [sp+0h] [bp-F8h]@1
  void *v10; // [sp+20h] [bp-D8h]@4
  void *Dst; // [sp+30h] [bp-C8h]@4
  unsigned int v12; // [sp+38h] [bp-C0h]@4
  char *DstBuf; // [sp+40h] [bp-B8h]@4
  int v14; // [sp+48h] [bp-B0h]@4
  char *Str; // [sp+50h] [bp-A8h]@4
  char pbyType[2]; // [sp+64h] [bp-94h]@4
  int v17; // [sp+74h] [bp-84h]@4
  RECV_DATA *_Val; // [sp+78h] [bp-80h]@9
  void *v19; // [sp+80h] [bp-78h]@4
  char *v20; // [sp+88h] [bp-70h]@4
  char *v21; // [sp+90h] [bp-68h]@4
  void *v22; // [sp+98h] [bp-60h]@4
  void *v23; // [sp+A0h] [bp-58h]@4
  RECV_DATA *v24; // [sp+A8h] [bp-50h]@9
  RECV_DATA *v25; // [sp+B0h] [bp-48h]@6
  void *v26; // [sp+B8h] [bp-40h]@9
  void *v27; // [sp+C0h] [bp-38h]@9
  __int64 v28; // [sp+C8h] [bp-30h]@4
  size_t v29; // [sp+D0h] [bp-28h]@4
  size_t v30; // [sp+D8h] [bp-20h]@4
  RECV_DATA *v31; // [sp+E0h] [bp-18h]@7
  CEnglandBillingMgr *v32; // [sp+100h] [bp+8h]@1
  _param_cash_select *v33; // [sp+108h] [bp+10h]@1
  int v34; // [sp+110h] [bp+18h]@1

  v34 = nIdx;
  v33 = rParam;
  v32 = this;
  v3 = &v9;
  for ( i = 60LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v28 = -2LL;
  v19 = operator new[](0x200ui64);
  Dst = v19;
  memset_0(v19, 0, 0x200ui64);
  v10 = v33->in_szAcc;
  sprintf_s((char *)Dst, 0x104ui64, "|%d|%s\n", (unsigned int)(v34 + 1));
  v12 = strlen_0((const char *)Dst) - 1;
  v20 = (char *)operator new[](0xAui64);
  DstBuf = v20;
  memset_0(v20, 0, 0xAui64);
  sprintf_s(DstBuf, 0xAui64, "60%05d", v12);
  v29 = strlen_0(DstBuf);
  v5 = strlen_0((const char *)Dst);
  v14 = v5 + v29;
  v21 = (char *)operator new[](v5 + (signed int)v29 + 1);
  Str = v21;
  memset_0(v21, 0, v14 + 1);
  v10 = Dst;
  sprintf_s(Str, v14 + 1, "%s%s", DstBuf);
  v22 = DstBuf;
  operator delete(DstBuf);
  v23 = Dst;
  operator delete(Dst);
  strcpy(pbyType, "\x01");
  v30 = strlen_0(Str);
  v6 = CTSingleton<CEngNetworkBillEX>::Instance();
  v17 = CEngNetworkBillEX::Send(v6, pbyType, Str, v30);
  CLogFile::Write(&v32->m_logBill, "Cash Query : %s", Str);
  if ( v17 )
  {
    v25 = (RECV_DATA *)operator new(0x18ui64);
    if ( v25 )
    {
      RECV_DATA::RECV_DATA(v25);
      v31 = v8;
    }
    else
    {
      v31 = 0LL;
    }
    v24 = v31;
    _Val = v31;
    v31->bResult = 0;
    _Val->dwSeq = v34 + 1;
    _Val->wType = 60;
    _Val->pData = v33;
    std::deque<RECV_DATA,std::allocator<RECV_DATA>>::push_front(&g_vRecvData, _Val);
    v26 = _Val;
    operator delete(_Val);
    v27 = Str;
    operator delete(Str);
    result = 0LL;
  }
  else
  {
    ResumeThread(m_hThread);
    CLogFile::Write(&v32->m_logBill, "Cash Query Fail.");
    result = 1LL;
  }
  return result;
}


