/*
 * Function: ?_buybygold_buy_single_item_calc_price_discount@CashItemRemoteStore@@AEAAKPEAU_CashShop_fld@@E@Z
 * Address: 0x1402FFCE0
 */

__int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price_discount(CashItemRemoteStore *this, _CashShop_fld *pCsFld, char by<PERSON>verlapNum)
{
  __int16 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // [sp+0h] [bp-18h]@1
  int v7; // [sp+4h] [bp-14h]@6
  CashItemRemoteStore *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v3 = (__int16 *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 += 2;
  }
  v6 = 0;
  if ( pCsFld->m_nCsDiscount == -1 )
    v6 = v8->m_cde.m_ini.m_wCsDiscount;
  v7 = pCsFld->m_nCsPrice;
  v7 -= 10 * (signed int)floor((double)((unsigned int)v6 * v7 / 0x64) * 0.1 + 0.5);
  return (unsigned int)(unsigned __int8)byOverlapNum * v7;
}
