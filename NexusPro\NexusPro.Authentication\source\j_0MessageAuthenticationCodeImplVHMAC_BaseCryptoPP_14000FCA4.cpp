﻿/*
 * Function: j_??0?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@@XZ
 * Address: 0x14000FCA4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>(CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1> > *this)
{
  CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>(this);
}

