/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int_CAsyncLogInfo_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64____0___::_Hash_stdext::_Hmap_traits_int_CAsyncLogInfo_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64____0____::_1_::dtor$1
 * Address: 0x1403C2FD0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int_CAsyncLogInfo_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64____0___::_Hash_stdext::_Hmap_traits_int_CAsyncLogInfo_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64____0____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)(a2 + 40));
}
