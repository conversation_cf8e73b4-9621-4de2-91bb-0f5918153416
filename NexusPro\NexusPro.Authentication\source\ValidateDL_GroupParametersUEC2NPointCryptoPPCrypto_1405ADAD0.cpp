﻿/*
 * Function: ?Validate@?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@BBI@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1405ADAD0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int  CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::Validate(__int64 a1)
{
  return CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::Validate(a1 - *(DWORD *)(a1 - 4) - 280);
}


