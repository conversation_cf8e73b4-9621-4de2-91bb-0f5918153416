/*
 * Function: _std::_Uninit_copy_GUILD_BATTLE::CGuildBattleRewardItem_____ptr64_GUILD_BATTLE::CGuildBattleRewardItem_____ptr64_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem____::_1_::catch$0
 * Address: 0x1403D3330
 */

void __fastcall __noreturn std::_Uninit_copy_GUILD_BATTLE::CGuildBattleRewardItem_____ptr64_GUILD_BATTLE::CGuildBattleRewardItem_____ptr64_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 16i64 )
    std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::destroy(
      *(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> **)(i + 88),
      *(GUILD_BATTLE::CGuildBattleRewardItem **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
