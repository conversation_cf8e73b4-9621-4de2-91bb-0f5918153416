﻿/*
 * Function: j_?Validate@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@BFA@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x14000C37E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate()
{
  return CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate();
}

