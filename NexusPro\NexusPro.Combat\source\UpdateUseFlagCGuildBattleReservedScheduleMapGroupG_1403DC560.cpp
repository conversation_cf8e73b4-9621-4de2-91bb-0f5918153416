/*
 * Function: ?UpdateUseFlag@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@IK@Z
 * Address: 0x1403DC560
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::UpdateUseFlag(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiMapID, unsigned int dwID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedule *result; // rax@7
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_ppkReservedSchedule && v7->m_uiMapCnt > uiMapID && v7->m_ppkReservedSchedule[uiMapID] )
    result = GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseFlag(v7->m_ppkReservedSchedule[uiMapID], dwID);
  else
    result = 0i64;
  return result;
}
