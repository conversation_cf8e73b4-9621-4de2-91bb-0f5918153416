/*
 * Function: _ValidateImageBase
 * Address: 0x1404DE4C0
 */

__int64 __fastcall ValidateImageBase(char *pImageBase)
{
  __int64 result; // rax@2
  char *v2; // [sp+10h] [bp-18h]@3

  if ( *(_WORD *)pImageBase == 23117 )
  {
    v2 = &pImageBase[*((_DWORD *)pImageBase + 15)];
    if ( *(_DWORD *)v2 == 17744 )
      result = *((_WORD *)v2 + 12) == 523;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
