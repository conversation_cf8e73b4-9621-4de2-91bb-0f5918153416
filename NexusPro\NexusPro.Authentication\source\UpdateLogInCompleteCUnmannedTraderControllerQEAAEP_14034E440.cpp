﻿/*
 * Function: ?UpdateLogInComplete@CUnmannedTraderController@@EPEAD@Z
 * Address: 0x14034E440
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char  CUnmannedTraderController::UpdateLogInComplete(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  _SYSTEMTIME *kCurTime; // [sp+20h] [bp-68h]@8
  unsigned int dwTax; // [sp+28h] [bp-60h]@11
  _SYSTEMTIME *v8; // [sp+30h] [bp-58h]@11
  char *v9; // [sp+40h] [bp-48h]@4
  char Dst; // [sp+58h] [bp-30h]@4
  int j; // [sp+74h] [bp-14h]@4
  int v12; // [sp+78h] [bp-10h]@7

  v2 = &v5;
  for ( i = 32LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = pData;
  pData[8] = 1;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  for ( j = 0; j < *((_WORD *)v9 + 5); ++j )
  {
    v9[16 * j + 12] = 0;
    v12 = (unsigned __int8)v9[16 * j + 13];
    v12 -= 37;
    switch ( v12 )
    {
      case 0:
      case 45:
      case 46:
      case 53:
      case 54:
      case 57:
        kCurTime = (_SYSTEMTIME *)&Dst;
        if ( !CRFWorldDatabase::Update_UnmannedTraderItemState(
                pkDB,
                v9[9],
                *(DWORD *)&v9[16 * j + 20],
                v9[16 * j + 24],
                (_SYSTEMTIME *)&Dst) )
        {
          v9[16 * j + 12] = 1;
          v9[8] = 0;
        }
        break;
      case 55:
        v8 = (_SYSTEMTIME *)&Dst;
        dwTax = 0;
        LODWORD(kCurTime) = 0;
        if ( !CRFWorldDatabase::Update_UnmannedTraderResutlInfo(
                pkDB,
                v9[9],
                *(DWORD *)&v9[16 * j + 20],
                v9[16 * j + 24],
                0,
                0,
                (_SYSTEMTIME *)&Dst) )
        {
          v9[16 * j + 12] = 1;
          v9[8] = 0;
        }
        break;
      default:
        continue;
    }
  }
  return 0;
}


