﻿/*
 * Function: j_?OnRecvSession@HACKSHEILD_PARAM_ANTICP@@_NPEAVCHackShieldExSystem@@HE_KPEAD@Z
 * Address: 0x140008B89
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession(HACKSHEILD_PARAM_ANTICP *this, CHackShieldExSystem *mgr, int nIndex, char byProtocol, unsigned __int64 tSize, char *pMsg)
{
  return HACKSHEILD_PARAM_ANTICP::OnRecvSession(this, mgr, nIndex, byProtocol, tSize, pMsg);
}

