﻿/*
 * Function: ??0?$hash_map@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@@stdext@@@XZ
 * Address: 0x1403C17E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>(stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<std::pair<int const ,CAsyncLogInfo *> > *v3; // rax@4
  stdext::hash_compare<int,std::less<int> > *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  char v7; // [sp+21h] [bp-17h]@4
  std::allocator<std::pair<int const ,CAsyncLogInfo *> > *_Al; // [sp+28h] [bp-10h]@4
  stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 12LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>((std::allocator<std::pair<int const ,CAsyncLogInfo *> > *)&v6);
  _Al = v3;
  stdext::hash_compare<int,std::less<int>>::hash_compare<int,std::less<int>>((stdext::hash_compare<int,std::less<int> > *)&v7);
  stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>(
    (stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> >,0> > *)&v9->_Myfirstiter,
    v4,
    _Al);
}


