﻿/*
 * Function: ?construct@?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@XPEAU?$pair@$$CBHPEAVCAsyncLogInfo@@@2@AEBU32@@Z
 * Address: 0x1403C6EA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::allocator<std::pair<int const,CAsyncLogInfo *>>::construct(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *this, std::pair<int const ,CAsyncLogInfo *> *_Ptr, std::pair<int const ,CAsyncLogInfo *> *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(_Ptr, _Val);
}

