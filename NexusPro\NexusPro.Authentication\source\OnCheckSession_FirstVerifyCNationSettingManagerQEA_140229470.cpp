﻿/*
 * Function: ?OnCheckSession_FirstVerify@CNationSettingManager@@_NH@Z
 * Address: 0x140229470
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int  CNationSettingManager::OnCheckSession_FirstVerify(CNationSettingManager *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  INationGameGuardSystem *v6; // [sp+20h] [bp-18h]@5
  CNationSettingManager *v7; // [sp+40h] [bp+8h]@1
  int v8; // [sp+48h] [bp+10h]@1

  v8 = n;
  v7 = this;
  v2 = &v5;
  for ( i = 12LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CNationSettingData::GetGameGuardSystem(v7->m_pData) )
  {
    v6 = CNationSettingData::GetGameGuardSystem(v7->m_pData);
    result = ((int ( *)(INationGameGuardSystem *, _QWORD))v6->vfptr->OnCheckSession_FirstVerify)(
               v6,
               (unsigned int)v8);
  }
  else
  {
    result = 1;
  }
  return result;
}


