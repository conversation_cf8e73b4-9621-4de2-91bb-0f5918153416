﻿/*
 * Function: std::_Uninit_fill_n (STL template function)
 * Address: 0x1403C8890
 * Decompiled from RF Online
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Simplified function signature for compilation compatibility
// Original was a complex STL template instantiation
void  std_Uninit_fill_n_AsyncLogInfo(void* _First, unsigned __int64 _Count, void* _Val, void* _Al, int __formal, int a6)
{
  // Simplified variable declarations for compilation compatibility
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  void *v9; // [sp+20h] [bp-18h]@4 - simplified from complex STL iterator
  __int64 v10; // [sp+28h] [bp-10h]@4
  void *_Ptr; // [sp+40h] [bp+8h]@1 - simplified from complex STL iterator
  unsigned __int64 v12; // [sp+48h] [bp+10h]@1
  void *_Vala; // [sp+50h] [bp+18h]@1 - simplified from complex STL iterator
  void *v14; // [sp+58h] [bp+20h]@1 - simplified from complex STL allocator

  v14 = _Al;
  _Vala = _Val;
  v12 = _Count;
  _Ptr = _First;
  v6 = &v8;
  for ( i = 12LL; i; --i )
  {
    *(DWORD *)v6 = 0xCCCCCCCC; // 0xCCCCCCCC in hex
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = -2LL;
  v9 = _Ptr;
  while ( v12 )
  {
    // Simplified construct call - original was complex STL template
    // std::allocator construct call simplified for compilation
    if (v14 && _Ptr && _Vala) {
      // Basic memory copy operation to simulate construct
      memcpy(_Ptr, _Vala, sizeof(void*));
    }
    --v12;
    _Ptr = (char*)_Ptr + sizeof(void*); // Increment pointer by size of pointer
  }
}


