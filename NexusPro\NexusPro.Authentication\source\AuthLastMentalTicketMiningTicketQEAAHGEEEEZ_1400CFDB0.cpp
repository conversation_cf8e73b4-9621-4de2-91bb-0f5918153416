/*
 * Function: ?AuthLastMentalTicket@MiningTicket@@QEAAHGEEEE@Z
 * Address: 0x1400CFDB0
 */

__int64 __fastcall MiningTicket::AuthLastMentalTicket(MiningTicket *this, unsigned __int16 byCurrentYear, char byCurrent<PERSON>onth, char byCurrentDay, char byCurrentHour, char byNumOfTime)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v9; // [sp+0h] [bp-58h]@1
  MiningTicket::_AuthKeyTicket v10; // [sp+34h] [bp-24h]@6
  MiningTicket *Src; // [sp+60h] [bp+8h]@1

  Src = this;
  v6 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( Src->m_dwTakeLastMentalTicket.uiData )
  {
    MiningTicket::_AuthKeyTicket::Set(&v10, byCurrentYear, byCurrentMonth, byCurrentDay, byCurrentHour, byNumOfTime);
    result = MiningTicket::_AuthKeyTicket::operator!=(&v10, (MiningTicket::_AuthKeyTicket *)Src) == 0;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
