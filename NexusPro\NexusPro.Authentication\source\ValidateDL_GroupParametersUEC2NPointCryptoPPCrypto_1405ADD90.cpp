﻿/*
 * Function: ?Validate@?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@BJI@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1405ADD90
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char __fastcall CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::Validate(__int64 a1, __int64 a2, unsigned int a3)
{
  return CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::Validate(a1 - *(DWORD *)(a1 - 4) - 408, a2, a3);
}

