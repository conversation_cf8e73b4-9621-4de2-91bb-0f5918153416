/*
 * Function: ?UpdateWinLose@CGuildBattleRankManager@GUILD_BATTLE@@QEAA_NEKEK@Z
 * Address: 0x1403CAFF0
 */

char __fastcall GUILD_BATTLE::CGuildBattleRankManager::UpdateWinLose(GUILD_BATTLE::CGuildBattleRankManager *this, char byWinRace, unsigned int dwWinGuildSerial, char byLoseRace, unsigned int dwLoseGuildSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v8; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleRankManager *v9; // [sp+30h] [bp+8h]@1
  unsigned int dwGuildSerial; // [sp+40h] [bp+18h]@1

  dwGuildSerial = dwWinGuildSerial;
  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( (signed int)(unsigned __int8)byWinRace < 3 && (signed int)(unsigned __int8)byLoseRace < 3 )
  {
    if ( GUILD_BATTLE::CGuildBattleRankManager::CheckRecord(v9, dwWinGuildSerial)
      && GUILD_BATTLE::CGuildBattleRankManager::CheckRecord(v9, dwLoseGuildSerial) )
    {
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 0);
      if ( CRFWorldDatabase::UpdateWinGuildBattleResult(pkDB, dwGuildSerial, 3u) )
      {
        if ( CRFWorldDatabase::UpdateLoseGuildBattleResult(pkDB, dwLoseGuildSerial, 0) )
        {
          CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&pkDB->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
          result = 1;
        }
        else
        {
          CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&pkDB->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
          result = 0;
        }
      }
      else
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&pkDB->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
