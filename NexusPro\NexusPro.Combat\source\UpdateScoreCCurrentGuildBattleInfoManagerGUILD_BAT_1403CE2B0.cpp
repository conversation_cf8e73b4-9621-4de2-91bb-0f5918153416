/*
 * Function: ?UpdateScore@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAAXIEK@Z
 * Address: 0x1403CE2B0
 */

void __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, unsigned int uiMapID, char byColorInx, unsigned int dwScore)
{
  if ( this->m_bInit && this->m_uiMapCnt > uiMapID && (signed int)(unsigned __int8)byColorInx < 2 )
  {
    if ( byColorInx )
      this->m_pkInfo[uiMapID].dwRighBluetScore = dwScore;
    else
      this->m_pkInfo[uiMapID].dwLeftRedScore = dwScore;
    this->m_pbUpdate[uiMapID] = 1;
  }
}
