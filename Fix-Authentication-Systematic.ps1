param(
    [int]$BatchSize = 20,
    [switch]$TestBuild = $true,
    [switch]$DryRun = $false
)

Write-Host "NexusPro Authentication Module Systematic Fix" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host ""

# Configuration
$sourceDir = "NexusPro\NexusPro.Authentication\source"
$includeTemplate = @"
#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

"@

# Fix patterns
$fixPatterns = @{
    "_DWORD" = "DWORD"
    "_BYTE" = "BYTE"
    "_WORD" = "WORD"
    "_QWORD" = "QWORD"
    "-858993460" = "0xCCCCCCCC"
    "QEAA" = ""
    "UEAA" = ""
    "MEAA" = ""
    "AEAA" = ""
    "YEAA" = ""
    "PEAA" = ""
    "__fastcall" = ""
}

function Test-ModuleBuild {
    Write-Host "🔨 Testing build..." -ForegroundColor Yellow
    
    $projectPath = "NexusPro\NexusPro.Authentication\NexusPro.Authentication.vcxproj"
    $msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    
    if (!(Test-Path $projectPath)) {
        Write-Host "❌ Project file not found: $projectPath" -ForegroundColor Red
        return $false
    }
    
    if (!(Test-Path $msbuildPath)) {
        Write-Host "❌ MSBuild not found: $msbuildPath" -ForegroundColor Red
        return $false
    }
    
    try {
        $result = & $msbuildPath $projectPath /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ BUILD SUCCESS!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Build failed. Last error lines:" -ForegroundColor Red
            $result | Select-Object -Last 15 | ForEach-Object { 
                if ($_.ToString().Trim()) {
                    Write-Host "   $_" -ForegroundColor Red
                }
            }
            return $false
        }
    } catch {
        Write-Host "❌ Build test failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Add-RequiredIncludes {
    param([string]$FilePath)
    
    try {
        $content = Get-Content $FilePath -Raw -ErrorAction Stop
        
        # Check if includes already present
        if ($content -match "#include.*NexusProCommon\.h") {
            return $false
        }
        
        if ($DryRun) {
            Write-Host "    [DRY RUN] Would add includes" -ForegroundColor Yellow
            return $true
        }
        
        # Find insertion point
        $lines = Get-Content $FilePath
        $insertIndex = 0
        
        for ($i = 0; $i -lt $lines.Count; $i++) {
            $line = $lines[$i].Trim()
            if ($line -and $line -ne "*/" -and !$line.StartsWith("/*") -and !$line.StartsWith("//") -and !$line.StartsWith("*")) {
                $insertIndex = $i
                break
            }
        }
        
        # Insert includes
        $newLines = $lines[0..($insertIndex-1)] + $includeTemplate.Split("`n") + $lines[$insertIndex..($lines.Count-1)]
        $newLines | Set-Content $FilePath -Encoding UTF8
        
        return $true
        
    } catch {
        Write-Host "    ❌ Error adding includes: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Apply-PatternFixes {
    param([string]$FilePath)
    
    try {
        $content = Get-Content $FilePath -Raw -ErrorAction Stop
        $originalContent = $content
        
        if ($DryRun) {
            # Just check if fixes would be applied
            foreach ($pattern in $fixPatterns.Keys) {
                if ($content -match [regex]::Escape($pattern)) {
                    Write-Host "    [DRY RUN] Would fix pattern: $pattern" -ForegroundColor Yellow
                    return $true
                }
            }
            return $false
        }
        
        # Apply fixes
        foreach ($pattern in $fixPatterns.Keys) {
            $replacement = $fixPatterns[$pattern]
            $content = $content -replace [regex]::Escape($pattern), $replacement
        }
        
        # Fix i64 literals
        $content = $content -replace "(\d+)i64", '$1LL'
        
        # Save if changed
        if ($content -ne $originalContent) {
            $content | Set-Content $FilePath -Encoding UTF8
            return $true
        }
        
        return $false
        
    } catch {
        Write-Host "    ❌ Error applying fixes: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Process-SourceFile {
    param([string]$FilePath)
    
    $changes = @()
    
    try {
        # Add includes
        if (Add-RequiredIncludes -FilePath $FilePath) {
            $changes += "includes"
        }
        
        # Apply pattern fixes
        if (Apply-PatternFixes -FilePath $FilePath) {
            $changes += "patterns"
        }
        
        return @{
            Changed = ($changes.Count -gt 0)
            Changes = $changes
        }

    } catch {
        Write-Host "    ❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
        return @{
            Changed = $false
            Changes = @()
        }
    }
}

# Main execution
try {
    $startTime = Get-Date
    
    if ($DryRun) {
        Write-Host "🔍 DRY RUN MODE - No files will be modified" -ForegroundColor Yellow
        Write-Host ""
    }
    
    # Find source files
    if (!(Test-Path $sourceDir)) {
        Write-Host "❌ Source directory not found: $sourceDir" -ForegroundColor Red
        exit 1
    }
    
    $sourceFiles = Get-ChildItem $sourceDir -Filter "*.cpp"
    Write-Host "📁 Found $($sourceFiles.Count) source files" -ForegroundColor White
    Write-Host ""
    
    if ($sourceFiles.Count -eq 0) {
        Write-Host "⚠️  No source files found" -ForegroundColor Yellow
        exit 0
    }
    
    # Process in batches
    $totalBatches = [Math]::Ceiling($sourceFiles.Count / $BatchSize)
    $totalProcessed = 0
    $totalModified = 0
    
    Write-Host "🚀 Processing $($sourceFiles.Count) files in $totalBatches batches of $BatchSize" -ForegroundColor Cyan
    Write-Host ""
    
    for ($batchNum = 0; $batchNum -lt $totalBatches; $batchNum++) {
        $startIdx = $batchNum * $BatchSize
        $endIdx = [Math]::Min($startIdx + $BatchSize - 1, $sourceFiles.Count - 1)
        $batchFiles = $sourceFiles[$startIdx..$endIdx]
        
        Write-Host "📦 Batch $($batchNum + 1)/$totalBatches (files $($startIdx + 1)-$($endIdx + 1)):" -ForegroundColor Yellow
        
        $batchModified = 0
        
        foreach ($file in $batchFiles) {
            $fileName = $file.Name
            if ($fileName.Length -gt 50) {
                $fileName = $fileName.Substring(0, 47) + "..."
            }
            
            Write-Host "  🔧 $fileName" -ForegroundColor Gray
            
            $result = Process-SourceFile -FilePath $file.FullName
            $totalProcessed++
            
            if ($result.Changed) {
                $batchModified++
                $totalModified++
                $changeStr = $result.Changes -join ", "
                Write-Host "    ✅ Applied: $changeStr" -ForegroundColor Green
            } else {
                Write-Host "    ⚪ No changes needed" -ForegroundColor Gray
            }
        }
        
        Write-Host "  📊 Batch result: $batchModified/$($batchFiles.Count) files modified" -ForegroundColor White
        
        # Progress
        $progress = ($batchNum + 1) / $totalBatches * 100
        $elapsed = (Get-Date) - $startTime
        Write-Host "  📈 Progress: $($progress.ToString('F1'))% | Elapsed: $($elapsed.TotalSeconds.ToString('F1'))s" -ForegroundColor Cyan
        Write-Host ""
        
        # Test build every 5 batches or at the end
        if ($TestBuild -and !$DryRun -and ($batchModified -gt 0) -and ((($batchNum + 1) % 5 -eq 0) -or ($batchNum -eq ($totalBatches - 1)))) {
            Write-Host "🔨 Testing build after batch $($batchNum + 1)..." -ForegroundColor Yellow
            $buildSuccess = Test-ModuleBuild
            if (!$buildSuccess) {
                Write-Host "⚠️  Build failed, but continuing..." -ForegroundColor Yellow
            }
            Write-Host ""
        }
    }
    
    # Final summary
    $duration = (Get-Date) - $startTime
    Write-Host "=" * 50 -ForegroundColor Green
    Write-Host "🎯 AUTHENTICATION MODULE COMPLETION SUMMARY" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor Green
    Write-Host "⏱️  Duration: $($duration.TotalSeconds.ToString('F1')) seconds ($($duration.TotalMinutes.ToString('F1')) minutes)" -ForegroundColor White
    Write-Host "📊 Files processed: $totalProcessed" -ForegroundColor White
    Write-Host "📊 Files modified: $totalModified" -ForegroundColor White
    Write-Host "📊 Success rate: $(($totalModified/$totalProcessed*100).ToString('F1'))%" -ForegroundColor White
    Write-Host ""
    
    # Final build test
    if ($TestBuild -and !$DryRun) {
        Write-Host "🔨 Final build test..." -ForegroundColor Yellow
        $finalBuild = Test-ModuleBuild
        
        if ($finalBuild) {
            Write-Host "🎉 AUTHENTICATION MODULE COMPLETED SUCCESSFULLY!" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Module processed but build issues remain" -ForegroundColor Yellow
        }
    }
    
    Write-Host ""
    Write-Host "✅ Authentication module processing completed!" -ForegroundColor Green
    
} catch {
    Write-Error "Unexpected error: $($_.Exception.Message)"
    exit 1
}
