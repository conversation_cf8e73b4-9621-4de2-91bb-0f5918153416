/*
 * Function: ?CallProc_RFOnlineAuth@CRFCashItemDatabase@@QEAAHAEAU_param_cash_select@@@Z
 * Address: 0x140482430
 */

signed __int64 __fastcall CRFCashItemDatabase::CallProc_RFOnlineAuth(CRFCashItemDatabase *this, _param_cash_select *rParam)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  char DstBuf; // [sp+40h] [bp-148h]@4
  char v9; // [sp+41h] [bp-147h]@4
  SQLLEN v10; // [sp+158h] [bp-30h]@22
  __int16 v11; // [sp+164h] [bp-24h]@9
  unsigned __int8 v12; // [sp+168h] [bp-20h]@16
  unsigned __int8 v13; // [sp+169h] [bp-1Fh]@24
  unsigned __int64 v14; // [sp+178h] [bp-10h]@4
  CRFCashItemDatabase *v15; // [sp+190h] [bp+8h]@1
  _param_cash_select *v16; // [sp+198h] [bp+10h]@1

  v16 = rParam;
  v15 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v9, 0, 0xFFui64);
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "declare @out_amount int exec prc_rfonline_auth '%s', @s_amount = @out_amount output select @out_amount",
    rParam->in_szAcc);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &DstBuf);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v11 = SQLExecDirect_0(v15->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v12 = 0;
        if ( v11 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
          v12 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v12;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 1u, 4, &v16->out_dwCashAmount, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v13 = 0;
          if ( v11 == 100 )
          {
            v13 = 2;
          }
          else
          {
            SQLStmt = v15->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
            v13 = 1;
          }
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          result = v13;
        }
        else
        {
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &DstBuf);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
