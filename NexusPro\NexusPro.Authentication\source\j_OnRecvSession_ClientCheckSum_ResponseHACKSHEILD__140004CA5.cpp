﻿/*
 * Function: j_?OnRecvSession_ClientCheckSum_Response@HACKSHEILD_PARAM_ANTICP@@_N_KPEAD@Z
 * Address: 0x140004CA5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool  HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCheckSum_Response(HACKSHEILD_PARAM_ANTICP *this, unsigned __int64 tSize, char *pMsg)
{
  return HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCheckSum_Response(this, tSize, pMsg);
}


