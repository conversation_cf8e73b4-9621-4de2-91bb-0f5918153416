/*
 * Function: j_?begin@?$_Hash@V?$_Hmap_traits@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@XZ
 * Address: 0x1400049AD
 */

std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>::begin(stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> >,0> > *this, std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *result)
{
  return stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>::begin(
           this,
           result);
}
