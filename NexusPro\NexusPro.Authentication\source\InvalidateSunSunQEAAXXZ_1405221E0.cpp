﻿/*
 * Function: ?InvalidateSun@Sun@@XXZ
 * Address: 0x1405221E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall Sun::InvalidateSun(Sun *this)
{
  Sun *v1; // rbx@1
  __int64 v2; // rcx@1

  v1 = this;
  v2 = *((_QWORD *)this + 5);
  if ( v2 )
    (*(void (**)(void))(*(_QWORD *)v2 + 16LL))();
  *((_QWORD *)v1 + 5) = 0LL;
}

