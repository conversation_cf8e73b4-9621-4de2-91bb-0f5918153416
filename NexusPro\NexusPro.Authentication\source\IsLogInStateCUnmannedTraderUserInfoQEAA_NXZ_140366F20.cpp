﻿/*
 * Function: ?IsLogInState@CUnmannedTraderUserInfo@@_NXZ
 * Address: 0x140366F20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool  CUnmannedTraderUserInfo::IsLogInState(CUnmannedTraderUserInfo *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  CUnmannedTraderUserInfo *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4LL; i; --i )
  {
    *v1 = 0xCCCCCCCC;
    ++v1;
  }
  return v5->m_eState == 1;
}


