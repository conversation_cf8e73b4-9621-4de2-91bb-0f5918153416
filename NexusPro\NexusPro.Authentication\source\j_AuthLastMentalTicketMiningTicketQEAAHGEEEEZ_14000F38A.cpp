/*
 * Function: j_?AuthLastMentalTicket@MiningTicket@@QEAAHGEEEE@Z
 * Address: 0x14000F38A
 */

int __fastcall MiningTicket::AuthLastMentalTicket(MiningTicket *this, unsigned __int16 byCurrent<PERSON>ear, char by<PERSON>ur<PERSON><PERSON><PERSON><PERSON>, char byCurrentDay, char byCurrent<PERSON><PERSON>, char byNumOfTime)
{
  return MiningTicket::AuthLastMentalTicket(
           this,
           byCurrent<PERSON>ear,
           byCurrentMonth,
           byCurrentDay,
           byCurrentHour,
           byNumOfTime);
}
