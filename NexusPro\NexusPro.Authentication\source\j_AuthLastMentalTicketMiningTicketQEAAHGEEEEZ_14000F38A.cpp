﻿/*
 * Function: j_?AuthLastMentalTicket@MiningTicket@@HGEEEE@Z
 * Address: 0x14000F38A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int  MiningTicket::AuthLastMentalTicket(MiningTicket *this, unsigned __int16 byCurrentYear, char byCurrent<PERSON><PERSON><PERSON>, char byCurrentDay, char byCurrentHour, char byNumOfTime)
{
  return MiningTicket::AuthLastMentalTicket(
           this,
           byCurrentYear,
           byCurrentMonth,
           byCurrentDay,
           byCurrentHour,
           byNumOfTime);
}


