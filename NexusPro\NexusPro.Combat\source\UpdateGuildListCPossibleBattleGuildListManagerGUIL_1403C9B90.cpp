/*
 * Function: ?UpdateGuildList@CPossibleBattleGuildListManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403C9B90
 */

void __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::UpdateGuildList(GUILD_BATTLE::CPossibleBattleGuildListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  unsigned __int16 wLastGuildInx; // [sp+24h] [bp-24h]@5
  unsigned int j; // [sp+34h] [bp-14h]@5
  int k; // [sp+38h] [bp-10h]@7
  unsigned __int8 l; // [sp+3Ch] [bp-Ch]@14
  GUILD_BATTLE::CPossibleBattleGuildListManager *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_bInit )
  {
    wLastGuildInx = 0;
    for ( j = 0; j < 3; ++j )
    {
      v8->m_pMaxPage[j] = 0;
      for ( k = 0; k < 75 && GUILD_BATTLE::CPossibleBattleGuildListManager::MakePage(v8, j, k, &wLastGuildInx); ++k )
        ++v8->m_pMaxPage[j];
      ++v8->m_pdwVer[j];
      wLastGuildInx = 0;
    }
    for ( j = 0; j < 3; ++j )
    {
      for ( l = 0; l < (signed int)v8->m_pMaxPage[j]; ++l )
      {
        v8->m_ppkList[j][l].byMaxPage = v8->m_pMaxPage[j];
        v8->m_ppkList[j][l].dwCurVer = v8->m_pdwVer[j];
      }
    }
  }
}
