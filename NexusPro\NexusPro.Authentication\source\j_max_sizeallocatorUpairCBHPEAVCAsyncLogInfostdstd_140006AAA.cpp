﻿/*
 * Function: j_?max_size@?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x140006AAA
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

unsigned __int64  std::allocator<std::pair<int const,CAsyncLogInfo *>>::max_size(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *this)
{
  return std::allocator<std::pair<int const,CAsyncLogInfo *>>::max_size(this);
}


