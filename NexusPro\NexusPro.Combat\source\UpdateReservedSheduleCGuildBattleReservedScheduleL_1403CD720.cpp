/*
 * Function: ?UpdateReservedShedule@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAA_NKPEAE@Z
 * Address: 0x1403CD720
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateReservedShedule(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int dwSLID, char *byOutData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  void *Dst; // [sp+20h] [bp-18h]@4
  unsigned int uiStartSLID; // [sp+48h] [bp+10h]@1

  uiStartSLID = dwSLID;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  Dst = byOutData;
  memset_0(byOutData, 0, 0x95Cui64);
  return CRFWorldDatabase::SelectGuildBattleRerservedList(
           pkDB,
           uiStartSLID,
           uiStartSLID,
           (_worlddb_guild_battle_reserved_schedule_info *)Dst) != 0;
}
