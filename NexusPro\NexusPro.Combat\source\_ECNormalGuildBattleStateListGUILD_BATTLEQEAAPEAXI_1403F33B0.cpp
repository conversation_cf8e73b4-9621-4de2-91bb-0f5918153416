/*
 * Function: ??_ECNormalGuildBattleStateList@GUILD_BATTLE@@QEAAPEAXI@Z
 * Address: 0x1403F33B0
 */

GUILD_BATTLE::CNormalGuildBattleStateList *__fastcall GUILD_BATTLE::CNormalGuildBattleStateList::`vector deleting destructor'(GUILD_BATTLE::CNormalGuildBattleStateList *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleStateList *result; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateList *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0x130ui64,
      (int)ptr[-1].m_pStateList[6],
      (void (__cdecl *)(void *))GUILD_BATTLE::CNormalGuildBattleStateList::~CNormalGuildBattleStateList);
    if ( v7 & 1 )
      operator delete[](&ptr[-1].m_pStateList[6]);
    result = (GUILD_BATTLE::CNormalGuildBattleStateList *)((char *)ptr - 8);
  }
  else
  {
    GUILD_BATTLE::CNormalGuildBattleStateList::~CNormalGuildBattleStateList(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
