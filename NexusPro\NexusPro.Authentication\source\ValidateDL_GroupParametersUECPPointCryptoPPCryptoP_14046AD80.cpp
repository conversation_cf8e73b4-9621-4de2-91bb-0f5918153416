/*
 * Function: ?Validate@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@BFA@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x14046AD80
 */

bool __fastcall CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate(__int64 a1, CryptoPP::RandomNumberGenerator *a2, unsigned int a3)
{
  return CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate(
           (CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *)(a1 - *(_DWORD *)(a1 - 4) - 336),
           a2,
           a3);
}
