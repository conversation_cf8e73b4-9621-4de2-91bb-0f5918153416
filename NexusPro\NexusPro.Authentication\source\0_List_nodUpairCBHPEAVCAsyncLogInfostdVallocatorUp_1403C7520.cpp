﻿/*
 * Function: ??0?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@IEAA@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@1@@Z
 * Address: 0x1403C7520
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>(std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *v5; // [sp+30h] [bp+8h]@1
  std::allocator<std::pair<int const ,CAsyncLogInfo *> > *__formal; // [sp+38h] [bp+10h]@1

  __formal = (std::allocator<std::pair<int const ,CAsyncLogInfo *> > *)_Al;
  v5 = this;
  v2 = &v4;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>(
    &v5->_Alnod,
    __formal);
}

