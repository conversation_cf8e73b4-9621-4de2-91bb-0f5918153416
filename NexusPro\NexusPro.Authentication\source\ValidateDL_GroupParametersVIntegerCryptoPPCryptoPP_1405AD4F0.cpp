﻿/*
 * Function: ?Validate@?$DL_GroupParameters@VInteger@CryptoPP@@@CryptoPP@@$4PPPPPPPM@MA@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1405AD4F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char  CryptoPP::DL_GroupParameters<CryptoPP::Integer>::Validate(__int64 a1, __int64 a2, unsigned int a3)
{
  return CryptoPP::DL_GroupParameters<CryptoPP::Integer>::Validate(a1 - *(DWORD *)(a1 - 4) - 192, a2, a3);
}


