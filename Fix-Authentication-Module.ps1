Write-Host "Fixing Authentication Module" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green

$sourceDir = "NexusPro\NexusPro.Authentication\source"
$includeText = @"
#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

"@

# Get all source files
$allFiles = Get-ChildItem $sourceDir -Filter "*.cpp"
Write-Host "Found $($allFiles.Count) source files to process" -ForegroundColor White

# Process in small batches
$batchSize = 10
$totalBatches = [Math]::Ceiling($allFiles.Count / $batchSize)
$processedFiles = 0
$modifiedFiles = 0

for ($batch = 0; $batch -lt $totalBatches; $batch++) {
    $startIndex = $batch * $batchSize
    $endIndex = [Math]::Min($startIndex + $batchSize - 1, $allFiles.Count - 1)
    $currentBatch = $allFiles[$startIndex..$endIndex]
    
    Write-Host ""
    Write-Host "Processing batch $($batch + 1)/$totalBatches (files $($startIndex + 1)-$($endIndex + 1))..." -ForegroundColor Yellow
    
    foreach ($file in $currentBatch) {
        Write-Host "  Processing: $($file.Name)" -ForegroundColor Gray
        
        try {
            $content = Get-Content $file.FullName -Raw -ErrorAction Stop
            $originalContent = $content
            $changed = $false
            
            # Add includes if missing
            if ($content -notmatch "#include.*NexusProCommon\.h") {
                $lines = Get-Content $file.FullName
                $insertIndex = 0
                
                # Find insertion point
                for ($i = 0; $i -lt $lines.Count; $i++) {
                    $line = $lines[$i].Trim()
                    if ($line -and $line -ne "*/" -and !$line.StartsWith("/*") -and !$line.StartsWith("//") -and !$line.StartsWith("*")) {
                        $insertIndex = $i
                        break
                    }
                }
                
                # Insert includes
                $newLines = $lines[0..($insertIndex-1)] + $includeText.Split("`n") + $lines[$insertIndex..($lines.Count-1)]
                $newLines | Set-Content $file.FullName -Encoding UTF8
                $changed = $true
                Write-Host "    Added includes" -ForegroundColor Cyan
            }
            
            # Apply basic fixes
            $content = Get-Content $file.FullName -Raw
            
            # Fix _DWORD -> DWORD
            if ($content -match "_DWORD") {
                $content = $content -replace "_DWORD", "DWORD"
                $changed = $true
                Write-Host "    Fixed _DWORD" -ForegroundColor Cyan
            }
            
            # Fix hex constants
            if ($content -match "-858993460") {
                $content = $content -replace "-858993460", "0xCCCCCCCC"
                $changed = $true
                Write-Host "    Fixed hex constant" -ForegroundColor Cyan
            }
            
            # Fix i64 literals
            if ($content -match "(\d+)i64") {
                $content = $content -replace "(\d+)i64", '$1LL'
                $changed = $true
                Write-Host "    Fixed i64 literals" -ForegroundColor Cyan
            }
            
            # Fix calling conventions
            $conventions = @("QEAA", "UEAA", "MEAA", "AEAA", "YEAA", "PEAA")
            foreach ($conv in $conventions) {
                if ($content -match $conv) {
                    $content = $content -replace $conv, ""
                    $changed = $true
                }
            }
            
            # Save changes
            if ($changed) {
                $content | Set-Content $file.FullName -Encoding UTF8
                $modifiedFiles++
                Write-Host "    Applied fixes" -ForegroundColor Green
            }
            
            $processedFiles++
            
        } catch {
            Write-Host "    Error processing file: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "Batch $($batch + 1) completed. Modified $modifiedFiles/$processedFiles files so far." -ForegroundColor White
    
    # Small delay between batches
    Start-Sleep -Milliseconds 100
}

Write-Host ""
Write-Host "=== AUTHENTICATION MODULE COMPLETION ===" -ForegroundColor Green
Write-Host "Total files processed: $processedFiles" -ForegroundColor White
Write-Host "Total files modified: $modifiedFiles" -ForegroundColor White

# Test build
Write-Host ""
Write-Host "Testing build..." -ForegroundColor Yellow
$msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
$projectPath = "NexusPro\NexusPro.Authentication\NexusPro.Authentication.vcxproj"

try {
    $buildResult = & $msbuildPath $projectPath /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "BUILD SUCCESS! Authentication module completed." -ForegroundColor Green
    } else {
        Write-Host "Build completed with errors. Check output above." -ForegroundColor Yellow
        $buildResult | Select-Object -Last 20 | ForEach-Object { Write-Host $_ -ForegroundColor Red }
    }
} catch {
    Write-Host "Build test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Authentication module processing completed!" -ForegroundColor Green
