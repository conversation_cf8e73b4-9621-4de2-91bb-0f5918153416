﻿/*
 * Function: ?Login@CBillingManager@@XPEAVCUserDB@@@Z
 * Address: 0x140079030
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CBillingManager::Login(CBillingManager *this, CUserDB *pUserDB)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CBillingManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ((void (__fastcall *)(CBilling *))v5->m_pBill->vfptr->Login)(v5->m_pBill);
}

