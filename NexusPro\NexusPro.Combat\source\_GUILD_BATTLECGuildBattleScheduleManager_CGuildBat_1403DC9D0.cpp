/*
 * Function: _GUILD_BATTLE::CGuildBattleScheduleManager::_CGuildBattleScheduleManager_::_1_::dtor$0
 * Address: 0x1403DC9D0
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::_CGuildBattleScheduleManager_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  `eh vector destructor iterator'(
    (void *)(*(_QWORD *)(a2 + 96) + 32i64),
    0x18ui64,
    2,
    (void (__cdecl *)(void *))GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::~CGuildBattleReservedScheduleMapGroup);
}
