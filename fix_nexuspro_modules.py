#!/usr/bin/env python3
"""
NexusPro Module Fix Script
Systematically applies fixes to RF Online decompiled source code
"""

import os
import re
import sys
import time
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple, Optional

class NexusProFixer:
    def __init__(self, project_root: str = "NexusPro"):
        self.project_root = Path(project_root)
        self.stats = {
            'files_processed': 0,
            'files_modified': 0,
            'errors': 0
        }
        
        # Module processing order (by dependency complexity)
        self.module_order = [
            "NexusPro.Authentication",
            "NexusPro.Network", 
            "NexusPro.Database",
            "NexusPro.Security",
            "NexusPro.Player",
            "NexusPro.Items",
            "NexusPro.Economy",
            "NexusPro.Combat",
            "NexusPro.World",
            "NexusPro.System"
        ]
        
        # Fix patterns based on our established solutions
        self.fix_patterns = {
            # Type fixes
            r'_DWORD': 'DWORD',
            r'_BYTE': 'BYTE',
            r'_WORD': 'WORD',
            r'_QWORD': 'QWORD',
            
            # Calling convention markers (remove these)
            r'QEAA': '',
            r'UEAA': '',
            r'MEAA': '',
            r'AEAA': '',
            r'YEAA': '',
            r'PEAA': '',
            
            # Hex constants
            r'-858993460': '0xCCCCCCCC',
            
            # Common patterns
            r'operator delete\[\]': 'delete[]',
            r'__fastcall': '',
        }
        
        # Complex regex patterns
        self.complex_patterns = [
            # Fix numeric literals
            (r'(\d+)i64', r'\1LL'),
            
            # Fix constructor signatures
            (r'void __fastcall (\w+)::(\w+)\(\1 \*this\)', r'\1::\2()'),
            
            # Simplify complex STL template signatures
            (r'std::.*<.*>::.*<.*>', 'void*'),
        ]
        
        self.include_template = '''#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

'''

    def print_header(self):
        """Print script header"""
        print("=" * 60)
        print("NexusPro Systematic Module Completion Script")
        print("=" * 60)
        print(f"Project root: {self.project_root.absolute()}")
        print(f"Modules to process: {len(self.module_order)}")
        print()

    def find_source_files(self, module_path: Path) -> List[Path]:
        """Find all C++ source files in a module"""
        source_dir = module_path / "source"
        if not source_dir.exists():
            return []
        
        return list(source_dir.glob("*.cpp"))

    def needs_includes(self, file_path: Path) -> bool:
        """Check if file needs our common includes"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                return 'NexusProCommon.h' not in content
        except Exception:
            return False

    def add_includes(self, file_path: Path) -> bool:
        """Add required includes to a source file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # Find insertion point (after initial comments)
            insert_index = 0
            for i, line in enumerate(lines):
                stripped = line.strip()
                if (stripped and 
                    stripped != '*/' and 
                    not stripped.startswith('/*') and 
                    not stripped.startswith('//') and 
                    not stripped.startswith('*')):
                    insert_index = i
                    break
            
            # Insert includes
            new_lines = (lines[:insert_index] + 
                        [self.include_template] + 
                        lines[insert_index:])
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            return True
            
        except Exception as e:
            print(f"    Error adding includes: {e}")
            return False

    def apply_pattern_fixes(self, file_path: Path) -> bool:
        """Apply all fix patterns to a file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            original_content = content
            
            # Apply simple string replacements
            for pattern, replacement in self.fix_patterns.items():
                content = re.sub(pattern, replacement, content)
            
            # Apply complex regex patterns
            for pattern, replacement in self.complex_patterns:
                content = re.sub(pattern, replacement, content)
            
            # Save if changed
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
            
            return False
            
        except Exception as e:
            print(f"    Error applying fixes: {e}")
            return False

    def process_file(self, file_path: Path) -> bool:
        """Process a single source file"""
        changed = False
        
        try:
            # Add includes if needed
            if self.needs_includes(file_path):
                if self.add_includes(file_path):
                    changed = True
                    print(f"    Added includes")
            
            # Apply pattern fixes
            if self.apply_pattern_fixes(file_path):
                changed = True
                print(f"    Applied pattern fixes")
            
            self.stats['files_processed'] += 1
            if changed:
                self.stats['files_modified'] += 1
            
            return changed
            
        except Exception as e:
            print(f"    ERROR: {e}")
            self.stats['errors'] += 1
            return False

    def test_module_build(self, module_name: str) -> bool:
        """Test if a module builds successfully"""
        project_path = self.project_root / module_name / f"{module_name}.vcxproj"
        
        if not project_path.exists():
            print(f"    Project file not found: {project_path}")
            return False
        
        msbuild_path = r"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
        
        if not Path(msbuild_path).exists():
            print(f"    MSBuild not found at: {msbuild_path}")
            return False
        
        try:
            print(f"    Testing build for {module_name}...")
            result = subprocess.run([
                msbuild_path,
                str(project_path),
                "/p:Configuration=Debug",
                "/p:Platform=x64",
                "/v:minimal",
                "/nologo"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"    ✅ BUILD SUCCESS!")
                return True
            else:
                print(f"    ❌ Build failed:")
                # Show last few lines of error output
                error_lines = result.stderr.split('\n')[-10:]
                for line in error_lines:
                    if line.strip():
                        print(f"      {line}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"    ⏰ Build timeout")
            return False
        except Exception as e:
            print(f"    ❌ Build test failed: {e}")
            return False

    def process_module(self, module_name: str, batch_size: int = 25, test_build: bool = True) -> bool:
        """Process all files in a module"""
        print(f"\n{'='*20} PROCESSING MODULE: {module_name} {'='*20}")
        
        module_path = self.project_root / module_name
        if not module_path.exists():
            print(f"❌ Module path not found: {module_path}")
            return False
        
        # Find source files
        source_files = self.find_source_files(module_path)
        if not source_files:
            print(f"⚠️  No source files found in {module_path}/source")
            return True
        
        print(f"📁 Found {len(source_files)} source files")
        
        # Process files in batches
        total_batches = (len(source_files) + batch_size - 1) // batch_size
        module_modified = 0
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(source_files))
            batch_files = source_files[start_idx:end_idx]
            
            print(f"\n📦 Batch {batch_num + 1}/{total_batches} (files {start_idx + 1}-{end_idx}):")
            
            batch_modified = 0
            for file_path in batch_files:
                print(f"  🔧 {file_path.name}")
                if self.process_file(file_path):
                    batch_modified += 1
                    module_modified += 1
            
            print(f"  📊 Batch result: {batch_modified}/{len(batch_files)} files modified")
            
            # Test build after each batch if requested
            if test_build and batch_modified > 0:
                if not self.test_module_build(module_name):
                    print(f"  ⚠️  Build failed after batch {batch_num + 1}")
                    # Continue anyway for now
        
        print(f"\n✅ Module {module_name} completed:")
        print(f"   📊 Files processed: {len(source_files)}")
        print(f"   📊 Files modified: {module_modified}")
        
        # Final build test
        if test_build:
            print(f"\n🔨 Final build test for {module_name}...")
            return self.test_module_build(module_name)
        
        return True

    def run(self, start_module: str = "Authentication", batch_size: int = 25, test_builds: bool = True):
        """Run the complete module fixing process"""
        self.print_header()
        
        # Find starting point
        start_index = 0
        for i, module in enumerate(self.module_order):
            if start_module in module:
                start_index = i
                break
        
        modules_to_process = self.module_order[start_index:]
        print(f"🚀 Processing {len(modules_to_process)} modules starting from {start_module}")
        print()
        
        successful_modules = []
        failed_modules = []
        start_time = time.time()
        
        for module in modules_to_process:
            try:
                if self.process_module(module, batch_size, test_builds):
                    successful_modules.append(module)
                    print(f"✅ {module} completed successfully")
                else:
                    failed_modules.append(module)
                    print(f"❌ {module} failed")
                    
                    # Ask user if they want to continue
                    if failed_modules:
                        response = input(f"\n⚠️  {module} failed. Continue with next module? (y/n): ")
                        if response.lower() != 'y':
                            break
                            
            except KeyboardInterrupt:
                print(f"\n⚠️  Process interrupted by user")
                break
            except Exception as e:
                print(f"❌ Unexpected error processing {module}: {e}")
                failed_modules.append(module)
        
        # Final summary
        duration = time.time() - start_time
        print(f"\n{'='*60}")
        print("🎯 COMPLETION SUMMARY")
        print(f"{'='*60}")
        print(f"⏱️  Duration: {duration/60:.1f} minutes")
        print(f"📊 Files processed: {self.stats['files_processed']}")
        print(f"📊 Files modified: {self.stats['files_modified']}")
        print(f"📊 Errors: {self.stats['errors']}")
        print(f"✅ Successful modules: {len(successful_modules)}")
        print(f"❌ Failed modules: {len(failed_modules)}")
        
        if successful_modules:
            print(f"\n✅ Successful modules:")
            for module in successful_modules:
                print(f"   {module}")
        
        if failed_modules:
            print(f"\n❌ Failed modules:")
            for module in failed_modules:
                print(f"   {module}")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Fix NexusPro modules systematically')
    parser.add_argument('--start-module', default='Authentication', 
                       help='Module to start from (default: Authentication)')
    parser.add_argument('--batch-size', type=int, default=25,
                       help='Number of files to process per batch (default: 25)')
    parser.add_argument('--no-build-test', action='store_true',
                       help='Skip build testing')
    parser.add_argument('--project-root', default='NexusPro',
                       help='Project root directory (default: NexusPro)')
    
    args = parser.parse_args()
    
    fixer = NexusProFixer(args.project_root)
    fixer.run(
        start_module=args.start_module,
        batch_size=args.batch_size,
        test_builds=not args.no_build_test
    )

if __name__ == "__main__":
    main()
