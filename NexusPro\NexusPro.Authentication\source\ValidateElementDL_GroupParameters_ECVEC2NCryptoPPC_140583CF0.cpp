/*
 * Function: ?ValidateElement@?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@CryptoPP@@UEBA_NIAEBUEC2NPoint@2@PEBV?$DL_FixedBasePrecomputation@UEC2NPoint@CryptoPP@@@2@@Z
 * Address: 0x140583CF0
 */

char __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::ValidateElement(__int64 a1, unsigned int a2, struct CryptoPP::EC2NPoint *a3, __int64 *a4)
{
  CryptoPP::EC2N *v4; // rax@2
  __int64 v5; // rax@8
  __int64 v6; // rax@8
  __int64 v7; // rax@15
  __int64 v8; // rax@16
  struct CryptoPP::EC2NPoint *v9; // rax@16
  struct CryptoPP::EC2NPoint *v10; // rax@17
  char v12; // [sp+20h] [bp-188h]@5
  CryptoPP::EC2NPoint v13; // [sp+28h] [bp-180h]@18
  __int64 v14; // [sp+60h] [bp-148h]@15
  CryptoPP::EC2NPoint v15; // [sp+68h] [bp-140h]@8
  struct CryptoPP::EC2NPoint *v16; // [sp+A0h] [bp-108h]@18
  CryptoPP::EC2NPoint v17; // [sp+A8h] [bp-100h]@16
  CryptoPP::EC2NPoint v18; // [sp+E0h] [bp-C8h]@17
  int v19; // [sp+118h] [bp-90h]@1
  __int64 v20; // [sp+120h] [bp-88h]@1
  int v21; // [sp+128h] [bp-80h]@3
  const struct CryptoPP::Integer *v22; // [sp+130h] [bp-78h]@8
  __int64 v23; // [sp+138h] [bp-70h]@8
  __int64 v24; // [sp+140h] [bp-68h]@8
  __int64 v25; // [sp+148h] [bp-60h]@8
  __int64 v26; // [sp+150h] [bp-58h]@8
  int v27; // [sp+158h] [bp-50h]@9
  __int64 v28; // [sp+160h] [bp-48h]@16
  struct CryptoPP::EC2NPoint *v29; // [sp+168h] [bp-40h]@16
  struct CryptoPP::EC2NPoint *v30; // [sp+170h] [bp-38h]@16
  struct CryptoPP::EC2NPoint *v31; // [sp+178h] [bp-30h]@16
  struct CryptoPP::EC2NPoint *v32; // [sp+180h] [bp-28h]@17
  struct CryptoPP::EC2NPoint *v33; // [sp+188h] [bp-20h]@17
  int v34; // [sp+190h] [bp-18h]@24
  __int64 v35; // [sp+1B0h] [bp+8h]@1
  unsigned int v36; // [sp+1B8h] [bp+10h]@1
  struct CryptoPP::EC2NPoint *v37; // [sp+1C0h] [bp+18h]@1
  __int64 *v38; // [sp+1C8h] [bp+20h]@1

  v38 = a4;
  v37 = a3;
  v36 = a2;
  v35 = a1;
  v20 = -2i64;
  v19 = 0;
  v21 = !(unsigned __int8)(*(int (__fastcall **)(__int64, struct CryptoPP::EC2NPoint *))(*(_QWORD *)a1 + 152i64))(
                            a1,
                            a3)
     && (LODWORD(v4) = CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::GetCurve(v35),
         CryptoPP::EC2N::VerifyPoint(v4, v37));
  v12 = v21;
  if ( v36 >= 1 && v38 )
  {
    v27 = (_BYTE)v21
       && (v22 = CryptoPP::Integer::One(),
           v23 = *(_QWORD *)v35,
           LODWORD(v5) = (*(int (__fastcall **)(__int64))(v23 + 40))(v35),
           v24 = *v38,
           LODWORD(v6) = (*(int (__fastcall **)(__int64 *, CryptoPP::EC2NPoint *, __int64, const struct CryptoPP::Integer *))(v24 + 48))(
                           v38,
                           &v15,
                           v5,
                           v22),
           v25 = v6,
           v26 = v6,
           v19 |= 1u,
           CryptoPP::EC2NPoint::operator==(v6, (__int64)v37));
    v12 = v27;
    if ( v19 & 1 )
    {
      v19 &= 0xFFFFFFFE;
      CryptoPP::EC2NPoint::~EC2NPoint(&v15);
    }
  }
  if ( v36 >= 2 && v12 )
  {
    LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v35 + 64i64))(v35);
    v14 = v7;
    if ( v38 )
    {
      LODWORD(v8) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v35 + 40i64))(v35);
      v28 = *v38;
      LODWORD(v9) = (*(int (__fastcall **)(__int64 *, CryptoPP::EC2NPoint *, __int64, __int64))(v28 + 48))(
                      v38,
                      &v17,
                      v8,
                      v14);
      v29 = v9;
      v30 = v9;
      v19 |= 2u;
      v31 = v9;
    }
    else
    {
      LODWORD(v10) = (*(int (__fastcall **)(__int64, CryptoPP::EC2NPoint *, struct CryptoPP::EC2NPoint *, __int64))(*(_QWORD *)v35 + 32i64))(
                       v35,
                       &v18,
                       v37,
                       v14);
      v32 = v10;
      v33 = v10;
      v19 |= 4u;
      v31 = v10;
    }
    v16 = v31;
    CryptoPP::EC2NPoint::EC2NPoint(&v13, v31);
    if ( v19 & 4 )
    {
      v19 &= 0xFFFFFFFB;
      CryptoPP::EC2NPoint::~EC2NPoint(&v18);
    }
    if ( v19 & 2 )
    {
      v19 &= 0xFFFFFFFD;
      CryptoPP::EC2NPoint::~EC2NPoint(&v17);
    }
    v34 = v12
       && (unsigned __int8)(*(int (__fastcall **)(__int64, CryptoPP::EC2NPoint *))(*(_QWORD *)v35 + 152i64))(v35, &v13);
    v12 = v34;
    CryptoPP::EC2NPoint::~EC2NPoint(&v13);
  }
  return v12;
}
