﻿/*
 * Function: ??D?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEBAAEAU?$pair@$$CBHPEAVCAsyncLogInfo@@@2@XZ
 * Address: 0x1403C4310
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

std::pair<int const ,CAsyncLogInfo *> * std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::operator*(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Const_iterator<0>::operator*((std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *)&v5->_Mycont);
}


