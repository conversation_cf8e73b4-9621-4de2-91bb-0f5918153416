/*
 * Function: ?_db_Load_BattleTournamentInfo@CMainThread@@AEAAXXZ
 * Address: 0x1401B7210
 */

void __fastcall CMainThread::_db_Load_BattleTournamentInfo(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-4E8h]@1
  int __t; // [sp+30h] [bp-4B8h]@4
  char pwszCharName[17]; // [sp+34h] [bp-4B4h]@8
  char v6[1135]; // [sp+45h] [bp-4A3h]@8
  int nMax; // [sp+4B4h] [bp-34h]@4
  int j; // [sp+4B8h] [bp-30h]@5
  CBattleTournamentInfo *v9; // [sp+4C8h] [bp-20h]@8
  unsigned __int64 v10; // [sp+4D0h] [bp-18h]@4
  CMainThread *v11; // [sp+4F0h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 312i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v3 ^ _security_cookie;
  `vector constructor iterator'(&__t, 0x18ui64, 48, (void *(__cdecl *)(void *))TournamentWinner::TournamentWinner);
  nMax = 48;
  if ( CRFWorldDatabase::Select_BattleTournamentInfo(v11->m_pWorldDB, (TournamentWinner *)&__t, 48) )
  {
    CBattleTournamentInfo::SetLoad(&v11->m_BattleTournamentInfo, 1);
    for ( j = 0; j < nMax && *(&__t + 6 * j) != -1; ++j )
    {
      v9 = &v11->m_BattleTournamentInfo;
      if ( !CBattleTournamentInfo::SetWinnerInfo(
              &v11->m_BattleTournamentInfo,
              *(&__t + 6 * j),
              &pwszCharName[24 * j],
              v6[24 * j]) )
      {
        CLogFile::Write(&v11->m_logSystemError, "Battle Tournament Winner Info Setting Fail! Winner Info Disable!");
        CBattleTournamentInfo::SetLoad(&v11->m_BattleTournamentInfo, 0);
        return;
      }
    }
  }
  else
  {
    CLogFile::Write(&v11->m_logSystemError, "Can't Battle Tournament Winner Info Getting From DB!");
  }
}
