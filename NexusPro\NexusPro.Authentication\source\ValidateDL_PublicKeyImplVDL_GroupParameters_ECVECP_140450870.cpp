/*
 * Function: ?Validate@?$DL_PublicKeyImpl@V?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x140450870
 */

__int64 __fastcall CryptoPP::DL_PublicKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::Validate(CryptoPP::DL_PublicKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *this, CryptoPP::RandomNumberGenerator *rng, unsigned int level)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@4
  __int64 *v6; // rax@5
  __int64 v7; // rax@5
  __int64 v8; // rax@5
  __int64 v10; // [sp+0h] [bp-78h]@1
  char v11; // [sp+20h] [bp-58h]@4
  __int64 v12; // [sp+28h] [bp-50h]@4
  CryptoPP::ASN1ObjectVtbl *v13; // [sp+30h] [bp-48h]@4
  CryptoPP::ASN1ObjectVtbl *v14; // [sp+38h] [bp-40h]@5
  __int64 *v15; // [sp+40h] [bp-38h]@5
  CryptoPP::ASN1ObjectVtbl *v16; // [sp+48h] [bp-30h]@5
  __int64 v17; // [sp+50h] [bp-28h]@5
  __int64 v18; // [sp+58h] [bp-20h]@5
  int v19; // [sp+60h] [bp-18h]@6
  CryptoPP::DL_PublicKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *v20; // [sp+80h] [bp+8h]@1
  CryptoPP::RandomNumberGenerator *v21; // [sp+88h] [bp+10h]@1
  unsigned int v22; // [sp+90h] [bp+18h]@1

  v22 = level;
  v21 = rng;
  v20 = this;
  v3 = &v10;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = v20[-1].vfptr;
  LODWORD(v5) = ((int (__fastcall *)(CryptoPP::ASN1ObjectVtbl **))v13->__vecDelDtor)(&v20[-1].vfptr);
  v12 = v5;
  v11 = (*(int (__fastcall **)(__int64, CryptoPP::RandomNumberGenerator *, _QWORD))(*(_QWORD *)(v5
                                                                                              + *(_DWORD *)(*(_QWORD *)(v5 + 8) + 4i64)
                                                                                              + 8)
                                                                                  + 24i64))(
          v12 + *(_DWORD *)(*(_QWORD *)(v12 + 8) + 4i64) + 8,
          v21,
          v22);
  v19 = v11
     && (v14 = v20[-1].vfptr,
         LODWORD(v6) = ((int (__fastcall *)(CryptoPP::ASN1ObjectVtbl **))v14->__vecDelDtor)(&v20[-1].vfptr),
         v15 = v6,
         v16 = v20[-1].vfptr,
         LODWORD(v7) = ((int (__fastcall *)(signed __int64))v16[1].DEREncode)((signed __int64)&v20[-1].vfptr),
         v17 = v7,
         LODWORD(v8) = ((int (__fastcall *)(signed __int64))v20[-1].vfptr->DEREncode)((signed __int64)&v20[-1].vfptr),
         v18 = *v15,
         (unsigned __int8)(*(int (__fastcall **)(__int64 *, _QWORD, __int64, __int64))(v18 + 136))(v15, v22, v8, v17));
  return (unsigned __int8)v19;
}
