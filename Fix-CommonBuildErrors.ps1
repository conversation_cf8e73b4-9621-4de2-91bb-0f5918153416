param(
    [string]$ProjectPath = "NexusPro",
    [switch]$AnalyzeOnly = $false,
    [switch]$FixIncludes = $true,
    [switch]$FixSyntax = $true
)

Write-Host "NexusPro Build Error Fix Script" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Common patterns to fix
$CommonFixes = @{
    # Fix missing includes
    "_DWORD" = "DWORD"
    "_BYTE" = "BYTE" 
    "_WORD" = "WORD"
    "_QWORD" = "QWORD"
    
    # Fix function naming issues
    "QEAA" = ""
    "UEAA" = ""
    "MEAA" = ""
    "AEAA" = ""
    "YEAA" = ""
    "PEAA" = ""
}

$IncludeTemplate = @"
#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

"@

function Add-CommonIncludes {
    param([string]$FilePath)
    
    $content = Get-Content $FilePath -Raw
    
    # Check if includes are already present
    if ($content -notmatch "#include.*NexusProCommon\.h") {
        Write-Host "Adding includes to: $FilePath" -ForegroundColor Yellow
        
        # Find the first non-comment line
        $lines = Get-Content $FilePath
        $insertIndex = 0
        
        for ($i = 0; $i -lt $lines.Count; $i++) {
            $line = $lines[$i].Trim()
            if ($line -and !$line.StartsWith("/*") -and !$line.StartsWith("//") -and !$line.StartsWith("*")) {
                $insertIndex = $i
                break
            }
        }
        
        # Insert includes
        $newContent = $lines[0..($insertIndex-1)] + $IncludeTemplate.Split("`n") + $lines[$insertIndex..($lines.Count-1)]
        $newContent | Set-Content $FilePath -Encoding UTF8
        
        return $true
    }
    
    return $false
}

function Fix-CommonSyntaxErrors {
    param([string]$FilePath)
    
    $content = Get-Content $FilePath -Raw
    $originalContent = $content
    $changed = $false
    
    # Apply common fixes
    foreach ($pattern in $CommonFixes.Keys) {
        $replacement = $CommonFixes[$pattern]
        if ($content -match [regex]::Escape($pattern)) {
            Write-Host "  Fixing pattern: $pattern -> $replacement" -ForegroundColor Cyan
            $content = $content -replace [regex]::Escape($pattern), $replacement
            $changed = $true
        }
    }
    
    # Fix complex STL template signatures
    if ($content -match "std::.*::\w+<.*>::.*<.*>") {
        Write-Host "  Simplifying complex STL templates" -ForegroundColor Cyan
        # This is a placeholder - would need more sophisticated regex
        $changed = $true
    }
    
    if ($changed) {
        $content | Set-Content $FilePath -Encoding UTF8
        Write-Host "  Fixed syntax errors in: $FilePath" -ForegroundColor Green
    }
    
    return $changed
}

function Analyze-BuildErrors {
    param([string]$ProjectPath)
    
    Write-Host "Analyzing source files in: $ProjectPath" -ForegroundColor Yellow
    
    $sourceFiles = Get-ChildItem -Path $ProjectPath -Recurse -Filter "*.cpp" | Where-Object { $_.Name -notlike "*test*" }
    $headerFiles = Get-ChildItem -Path $ProjectPath -Recurse -Filter "*.h" | Where-Object { $_.Name -notlike "*test*" }
    
    Write-Host "Found $($sourceFiles.Count) source files and $($headerFiles.Count) header files" -ForegroundColor White
    
    $problemFiles = @()
    $commonIssues = @{}
    
    foreach ($file in $sourceFiles) {
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content) {
            $issues = @()
            
            # Check for missing includes
            if ($content -notmatch "#include.*NexusProCommon\.h") {
                $issues += "Missing common includes"
            }
            
            # Check for problematic patterns
            foreach ($pattern in $CommonFixes.Keys) {
                if ($content -match [regex]::Escape($pattern)) {
                    $issues += "Contains pattern: $pattern"
                    if (!$commonIssues.ContainsKey($pattern)) {
                        $commonIssues[$pattern] = 0
                    }
                    $commonIssues[$pattern]++
                }
            }
            
            # Check for complex STL signatures
            if ($content -match "std::.*::\w+<.*>::.*<.*>") {
                $issues += "Complex STL template signatures"
            }
            
            if ($issues.Count -gt 0) {
                $problemFiles += [PSCustomObject]@{
                    File = $file.FullName
                    Issues = $issues
                }
            }
        }
    }
    
    Write-Host "`nAnalysis Results:" -ForegroundColor Green
    Write-Host "=================" -ForegroundColor Green
    Write-Host "Files with issues: $($problemFiles.Count)" -ForegroundColor White
    
    if ($commonIssues.Count -gt 0) {
        Write-Host "`nMost common issues:" -ForegroundColor Yellow
        $commonIssues.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
            Write-Host "  $($_.Key): $($_.Value) files" -ForegroundColor White
        }
    }
    
    return $problemFiles
}

# Main execution
try {
    if ($AnalyzeOnly) {
        $problemFiles = Analyze-BuildErrors -ProjectPath $ProjectPath
        Write-Host "`nAnalysis complete. Use -FixIncludes and -FixSyntax to apply fixes." -ForegroundColor Green
        exit 0
    }
    
    Write-Host "Starting build error fixes..." -ForegroundColor Yellow
    
    $sourceFiles = Get-ChildItem -Path $ProjectPath -Recurse -Filter "*.cpp" | Where-Object { $_.Name -notlike "*test*" }
    $fixedFiles = 0
    
    foreach ($file in $sourceFiles) {
        $changed = $false
        
        if ($FixIncludes) {
            if (Add-CommonIncludes -FilePath $file.FullName) {
                $changed = $true
            }
        }
        
        if ($FixSyntax) {
            if (Fix-CommonSyntaxErrors -FilePath $file.FullName) {
                $changed = $true
            }
        }
        
        if ($changed) {
            $fixedFiles++
        }
    }
    
    Write-Host "`nFixed $fixedFiles files" -ForegroundColor Green
    Write-Host "Build error fix complete!" -ForegroundColor Green
    
} catch {
    Write-Error "Error during fix process: $($_.Exception.Message)"
    exit 1
}
