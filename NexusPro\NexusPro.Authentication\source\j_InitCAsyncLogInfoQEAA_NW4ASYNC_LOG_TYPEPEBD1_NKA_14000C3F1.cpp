﻿/*
 * Function: j_?Init@CAsyncLogInfo@@_NW4ASYNC_LOG_TYPE@@PEBD1_NKAEAVCLogFile@@@Z
 * Address: 0x14000C3F1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool  CAsyncLogInfo::Init(CAsyncLogInfo *this, ASYNC_LOG_TYPE eType, const char *szDirPath, const char *szTypeName, bool bAddDateFileName, unsigned int dwUpdateFileNameDelay, CLogFile *logLoading)
{
  return CAsyncLogInfo::Init(this, eType, szDirPath, szTypeName, bAddDateFileName, dwUpdateFileNameDelay, logLoading);
}


