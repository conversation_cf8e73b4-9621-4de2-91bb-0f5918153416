/*
 * Function: ?Update_TrunkPassword@CUserDB@@QEAA_NPEAD@Z
 * Address: 0x140116FD0
 */

char __fastcall CUserDB::Update_TrunkPassword(CUserDB *this, char *pwszPassword)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *Dest; // [sp+20h] [bp-18h]@4
  CUserDB *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  Dest = v7->m_AvatorData.dbTrunk.wszPasswd;
  strcpy_0(v7->m_AvatorData.dbTrunk.wszPasswd, pwszPassword);
  v7->m_bDataUpdate = 1;
  return 1;
}
