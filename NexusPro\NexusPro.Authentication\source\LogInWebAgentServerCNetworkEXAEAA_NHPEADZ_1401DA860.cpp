/*
 * Function: ?LogInWebAgentServer@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401DA860
 */

char __fastcall CNetworkEX::LogInWebAgentServer(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-88h]@1
  char *v7; // [sp+30h] [bp-58h]@4
  char pbyType; // [sp+44h] [bp-44h]@4
  char v9; // [sp+45h] [bp-43h]@4
  char szMsg; // [sp+64h] [bp-24h]@4

  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = pBuf;
  pbyType = 51;
  v9 = 1;
  szMsg = 0;
  if ( unk_1799C9ADE )
  {
    szMsg = 1;
    CNetProcess::LoadSendMsg(unk_1414F2098, n, &pbyType, &szMsg, 1u);
    result = 1;
  }
  else if ( (unsigned __int8)*v7 == 237 )
  {
    unk_1799C9ADE = 1;
    unk_1799C9ADD = n;
    CNetProcess::LoadSendMsg(unk_1414F2098, n, &pbyType, &szMsg, 1u);
    result = 1;
  }
  else
  {
    szMsg = 2;
    CNetProcess::LoadSendMsg(unk_1414F2098, n, &pbyType, &szMsg, 1u);
    result = 1;
  }
  return result;
}
