/*
 * Function: ?ValidateGroup@DL_GroupParameters_DSA@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x140630230
 */

__int64 __fastcall CryptoPP::DL_GroupParameters_DSA::ValidateGroup(CryptoPP::DL_GroupParameters_DSA *this, struct CryptoPP::RandomNumberGenerator *a2, unsigned int a3)
{
  CryptoPP::Integer *v3; // rax@2
  unsigned int v4; // eax@2
  CryptoPP::Integer *v5; // rax@6
  bool v7; // [sp+30h] [bp-28h]@3
  bool v8; // [sp+40h] [bp-18h]@7
  CryptoPP::DL_GroupParameters_DSA *v9; // [sp+60h] [bp+8h]@1

  v9 = this;
  v7 = CryptoPP::DL_GroupParameters_IntegerBased::ValidateGroup(
         (CryptoPP::DL_GroupParameters_IntegerBased *)&this->vfptr,
         a2,
         a3)
    && (LODWORD(v3) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)&v9[-1].gapE0[8] + 32i64))((signed __int64)&v9[-1].gapE0[8]),
        v4 = CryptoPP::Integer::BitCount(v3),
        CryptoPP::DSA::IsValidPrimeLength(v4));
  v8 = v7
    && (LODWORD(v5) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_DSA *))v9->vfptr[2].__vecDelDtor)(v9),
        (unsigned int)CryptoPP::Integer::BitCount(v5) == 160);
  return v8;
}
