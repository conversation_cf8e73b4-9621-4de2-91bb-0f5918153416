﻿/*
 * Function: ?OnCheckSession_FirstVerify@CHackShieldExSystem@@_NH@Z
 * Address: 0x140417250
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool  CHackShieldExSystem::OnCheckSession_FirstVerify(CHackShieldExSystem *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  BASE_HACKSHEILD_PARAM *v6; // [sp+20h] [bp-18h]@4
  CHackShieldExSystem *v7; // [sp+40h] [bp+8h]@1
  int na; // [sp+48h] [bp+10h]@1

  na = n;
  v7 = this;
  v2 = &v5;
  for ( i = 12LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CHackShieldExSystem::GetParam(v7, n);
  if ( v6 )
    result = ((int ( *)(BASE_HACKSHEILD_PARAM *, _QWORD))v6->vfptr->OnCheckSession_FirstVerify)(
               v6,
               (unsigned int)na);
  else
    result = 0;
  return result;
}


