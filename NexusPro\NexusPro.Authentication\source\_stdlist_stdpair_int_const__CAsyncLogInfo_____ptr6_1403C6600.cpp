﻿/*
 * Function: _std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Buynode_::_1_::catch$0_0
 * Address: 0x1403C6600
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall __noreturn std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Buynode_::_1_::catch_0_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node **v3; // rax@2
  std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node **v4; // rax@4

  v2 = a2;
  if ( *(DWORD *)(a2 + 40) > 1 )
  {
    v3 = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Prevnode(
           *(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > **)(a2 + 32),
           (std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *)a2);
    std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *>::destroy(
      (std::allocator<std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *> *)(*(_QWORD *)(v2 + 96) + 16LL),
      v3);
  }
  if ( *(DWORD *)(v2 + 40) > 0 )
  {
    v4 = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Nextnode(
           *(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > **)(v2 + 32),
           (std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *)a2);
    std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *>::destroy(
      (std::allocator<std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *> *)(*(_QWORD *)(v2 + 96) + 16LL),
      v4);
  }
  std::allocator<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>::deallocate(
    (std::allocator<std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node> *)(*(_QWORD *)(v2 + 96) + 8LL),
    *(std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node **)(v2 + 32),
    1ui64);
  CxxThrowException_0(0LL, 0LL);
}

