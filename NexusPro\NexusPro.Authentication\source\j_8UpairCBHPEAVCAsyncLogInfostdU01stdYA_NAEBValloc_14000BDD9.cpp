﻿/*
 * Function: j_??$?8U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@U01@@std@@YA_NAEBV?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@0@0@Z
 * Address: 0x14000BDD9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool  std::operator==<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *__formal, std::allocator<std::pair<int const ,CAsyncLogInfo *> > *a2)
{
  return std::operator==<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(__formal, a2);
}


