#!/usr/bin/env python3
"""
NexusPro Authentication Module Fix Script
Focused script to fix the Authentication module systematically
"""

import os
import re
import sys
import time
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple

def print_header():
    """Print script header"""
    print("=" * 50)
    print("NexusPro Authentication Module Fix Script")
    print("=" * 50)
    print()

def find_source_files(source_dir: Path) -> List[Path]:
    """Find all C++ source files"""
    if not source_dir.exists():
        print(f"❌ Source directory not found: {source_dir}")
        return []
    
    files = list(source_dir.glob("*.cpp"))
    print(f"📁 Found {len(files)} source files")
    return files

def needs_includes(file_path: Path) -> bool:
    """Check if file needs our common includes"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            return 'NexusProCommon.h' not in content
    except Exception:
        return False

def add_includes(file_path: Path) -> bool:
    """Add required includes to a source file"""
    include_template = '''#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

'''
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Find insertion point (after initial comments)
        insert_index = 0
        for i, line in enumerate(lines):
            stripped = line.strip()
            if (stripped and 
                stripped != '*/' and 
                not stripped.startswith('/*') and 
                not stripped.startswith('//') and 
                not stripped.startswith('*')):
                insert_index = i
                break
        
        # Insert includes
        new_lines = lines[:insert_index] + [include_template] + lines[insert_index:]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        return True
        
    except Exception as e:
        print(f"    ❌ Error adding includes: {e}")
        return False

def apply_basic_fixes(file_path: Path) -> bool:
    """Apply basic pattern fixes to a file"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        original_content = content
        
        # Basic fixes
        fixes = [
            (r'_DWORD', 'DWORD'),
            (r'_BYTE', 'BYTE'),
            (r'_WORD', 'WORD'),
            (r'_QWORD', 'QWORD'),
            (r'-858993460', '0xCCCCCCCC'),
            (r'(\d+)i64', r'\1LL'),
            (r'QEAA', ''),
            (r'UEAA', ''),
            (r'MEAA', ''),
            (r'AEAA', ''),
            (r'YEAA', ''),
            (r'PEAA', ''),
            (r'__fastcall', ''),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        # Save if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"    ❌ Error applying fixes: {e}")
        return False

def process_file(file_path: Path) -> Tuple[bool, List[str]]:
    """Process a single source file"""
    changes = []
    
    try:
        # Add includes if needed
        if needs_includes(file_path):
            if add_includes(file_path):
                changes.append("includes")
        
        # Apply basic fixes
        if apply_basic_fixes(file_path):
            changes.append("patterns")
        
        return len(changes) > 0, changes
        
    except Exception as e:
        print(f"    ❌ ERROR: {e}")
        return False, []

def test_build() -> bool:
    """Test if the Authentication module builds"""
    project_path = Path("NexusPro/NexusPro.Authentication/NexusPro.Authentication.vcxproj")
    
    if not project_path.exists():
        print(f"❌ Project file not found: {project_path}")
        return False
    
    msbuild_path = r"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    
    if not Path(msbuild_path).exists():
        print(f"❌ MSBuild not found at: {msbuild_path}")
        return False
    
    try:
        print("🔨 Testing build...")
        result = subprocess.run([
            msbuild_path,
            str(project_path),
            "/p:Configuration=Debug",
            "/p:Platform=x64",
            "/v:minimal",
            "/nologo"
        ], capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0:
            print("✅ BUILD SUCCESS!")
            return True
        else:
            print("❌ Build failed. Last few error lines:")
            error_lines = result.stderr.split('\n')[-15:]
            for line in error_lines:
                if line.strip():
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Build timeout (3 minutes)")
        return False
    except Exception as e:
        print(f"❌ Build test failed: {e}")
        return False

def main():
    """Main function"""
    print_header()
    
    # Configuration
    source_dir = Path("NexusPro/NexusPro.Authentication/source")
    batch_size = 20
    
    # Find source files
    source_files = find_source_files(source_dir)
    if not source_files:
        return
    
    # Process files in batches
    total_batches = (len(source_files) + batch_size - 1) // batch_size
    total_processed = 0
    total_modified = 0
    start_time = time.time()
    
    print(f"🚀 Processing {len(source_files)} files in {total_batches} batches of {batch_size}")
    print()
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, len(source_files))
        batch_files = source_files[start_idx:end_idx]
        
        print(f"📦 Batch {batch_num + 1}/{total_batches} (files {start_idx + 1}-{end_idx}):")
        
        batch_modified = 0
        for file_path in batch_files:
            print(f"  🔧 {file_path.name[:50]}{'...' if len(file_path.name) > 50 else ''}")
            
            changed, changes = process_file(file_path)
            total_processed += 1
            
            if changed:
                batch_modified += 1
                total_modified += 1
                change_str = ", ".join(changes)
                print(f"    ✅ Applied: {change_str}")
            else:
                print(f"    ⚪ No changes needed")
        
        print(f"  📊 Batch result: {batch_modified}/{len(batch_files)} files modified")
        
        # Progress update
        progress = (batch_num + 1) / total_batches * 100
        elapsed = time.time() - start_time
        print(f"  📈 Progress: {progress:.1f}% | Elapsed: {elapsed:.1f}s")
        print()
        
        # Test build every 5 batches or at the end
        if (batch_num + 1) % 5 == 0 or batch_num == total_batches - 1:
            if batch_modified > 0:
                print(f"🔨 Testing build after batch {batch_num + 1}...")
                build_success = test_build()
                if not build_success:
                    print("⚠️  Build failed, but continuing...")
                print()
    
    # Final summary
    duration = time.time() - start_time
    print("=" * 50)
    print("🎯 AUTHENTICATION MODULE COMPLETION SUMMARY")
    print("=" * 50)
    print(f"⏱️  Duration: {duration:.1f} seconds ({duration/60:.1f} minutes)")
    print(f"📊 Files processed: {total_processed}")
    print(f"📊 Files modified: {total_modified}")
    print(f"📊 Success rate: {total_modified/total_processed*100:.1f}%")
    print()
    
    # Final build test
    print("🔨 Final build test...")
    final_build = test_build()
    
    if final_build:
        print("🎉 AUTHENTICATION MODULE COMPLETED SUCCESSFULLY!")
    else:
        print("⚠️  Module processed but build issues remain")
    
    print()
    print("✅ Authentication module processing completed!")

if __name__ == "__main__":
    main()
