﻿/*
 * Function: ?OnRecvSession_ServerCheckSum_Request@HACKSHEILD_PARAM_ANTICP@@_NH@Z
 * Address: 0x140417FB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char  HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(HACKSHEILD_PARAM_ANTICP *this, int nIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-B8h]@1
  __int16 Dst; // [sp+38h] [bp-80h]@4
  char v7; // [sp+3Ah] [bp-7Eh]@4
  unsigned int dwRet; // [sp+54h] [bp-64h]@4
  char pbyType; // [sp+64h] [bp-54h]@5
  char v10; // [sp+65h] [bp-53h]@5
  char v11; // [sp+84h] [bp-34h]@6
  char v12; // [sp+85h] [bp-33h]@6
  unsigned __int64 v13; // [sp+A0h] [bp-18h]@4
  HACKSHEILD_PARAM_ANTICP *v14; // [sp+C0h] [bp+8h]@1
  int dwClientIndex; // [sp+C8h] [bp+10h]@1

  dwClientIndex = nIndex;
  v14 = this;
  v2 = &v5;
  for ( i = 44LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  HACKSHEILD_PARAM_ANTICP::Init(v14);
  memset_0(&Dst, 0, 0x16ui64);
  dwRet = _AntiCpSvr_MakeGuidReqMsg(&v7, v14->m_byGUIDClientInfo);
  Dst = dwRet;
  if ( dwRet )
  {
    pbyType = 98;
    v10 = 2;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, (char *)&Dst, 0x16u);
    HACKSHEILD_PARAM_ANTICP::Kick(v14, 2, dwRet);
    HACKSHEILD_PARAM_ANTICP::Init(v14);
    result = 1;
  }
  else
  {
    v14->m_nSocketIndex = dwClientIndex;
    v14->m_byVerifyState = 1;
    v11 = 98;
    v12 = 2;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &v11, (char *)&Dst, 0x16u);
    result = 1;
  }
  return result;
}


