﻿/*
 * Function: ?Validate@?$DL_PrivateKeyImpl@V?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1404515F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

char __fastcall CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::Validate(CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *this, CryptoPP::RandomNumberGenerator *rng, unsigned int level)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@4
  __int64 v6; // rax@4
  CryptoPP::Integer *v7; // rax@4
  CryptoPP::Integer *v8; // rax@4
  __int64 v10; // [sp+0h] [bp-C8h]@1
  char v11; // [sp+20h] [bp-A8h]@4
  CryptoPP::Integer *b; // [sp+28h] [bp-A0h]@4
  CryptoPP::Integer *a; // [sp+30h] [bp-98h]@4
  __int64 v14; // [sp+38h] [bp-90h]@4
  CryptoPP::Integer v15; // [sp+40h] [bp-88h]@11
  int v16; // [sp+68h] [bp-60h]@4
  __int64 v17; // [sp+70h] [bp-58h]@4
  CryptoPP::ASN1ObjectVtbl *v18; // [sp+78h] [bp-50h]@4
  CryptoPP::ASN1ObjectVtbl *v19; // [sp+80h] [bp-48h]@4
  __int64 v20; // [sp+88h] [bp-40h]@4
  CryptoPP::ASN1ObjectVtbl *v21; // [sp+90h] [bp-38h]@4
  int v22; // [sp+98h] [bp-30h]@7
  CryptoPP::Integer *v23; // [sp+A0h] [bp-28h]@11
  CryptoPP::Integer *v24; // [sp+A8h] [bp-20h]@11
  CryptoPP::Integer *v25; // [sp+B0h] [bp-18h]@11
  int v26; // [sp+B8h] [bp-10h]@12
  CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *v27; // [sp+D0h] [bp+8h]@1
  CryptoPP::RandomNumberGenerator *v28; // [sp+D8h] [bp+10h]@1
  unsigned int v29; // [sp+E0h] [bp+18h]@1

  v29 = level;
  v28 = rng;
  v27 = this;
  v3 = &v10;
  for ( i = 48LL; i; --i )
  {
    *(DWORD *)v3 = 0xCCCCCCCC;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = -2LL;
  v16 = 0;
  v18 = v27[-1].vfptr;
  LODWORD(v5) = ((int (__fastcall *)(CryptoPP::ASN1ObjectVtbl **))v18->__vecDelDtor)(&v27[-1].vfptr);
  v14 = v5;
  v11 = (*(int (__fastcall **)(__int64, CryptoPP::RandomNumberGenerator *, _QWORD))(*(_QWORD *)(v5
                                                                                              + *(DWORD *)(*(_QWORD *)(v5 + 8) + 4LL)
                                                                                              + 8)
                                                                                  + 24LL))(
          v14 + *(DWORD *)(*(_QWORD *)(v14 + 8) + 4LL) + 8,
          v28,
          v29);
  v19 = v27[-1].vfptr;
  LODWORD(v6) = ((int (__fastcall *)(CryptoPP::ASN1ObjectVtbl **))v19->__vecDelDtor)(&v27[-1].vfptr);
  v20 = v6;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v6 + 64LL))(v6);
  b = v7;
  v21 = v27[-1].vfptr;
  LODWORD(v8) = ((int (__fastcall *)(signed __int64))v21->DEREncode)((signed __int64)&v27[-1].vfptr);
  a = v8;
  v22 = v11 && CryptoPP::Integer::IsPositive(a) && CryptoPP::operator<(a, b);
  v11 = v22;
  if ( v29 >= 1 )
  {
    v26 = v11
       && (v23 = (CryptoPP::Integer *)CryptoPP::Integer::One(),
           v24 = CryptoPP::Integer::Gcd(&v15, a, b),
           v25 = v24,
           v16 |= 1u,
           CryptoPP::operator==(v24, v23));
    v11 = v26;
    if ( v16 & 1 )
    {
      v16 &= 0xFFFFFFFE;
      CryptoPP::Integer::~Integer(&v15);
    }
  }
  return v11;
}

