/*
 * Function: ?LogIn@CNormalGuildBattle@GUILD_BATTLE@@QEAAXHKK@Z
 * Address: 0x1403E4050
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::LogIn(GUILD_BATTLE::CNormalGuildBattle *this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@5
  char *v7; // rax@7
  __int64 v8; // [sp+0h] [bp-58h]@1
  char *wszDestGuild; // [sp+20h] [bp-38h]@5
  unsigned int uiID; // [sp+28h] [bp-30h]@5
  GUILD_BATTLE::CNormalGuildBattleField *pkField; // [sp+30h] [bp-28h]@5
  GUILD_BATTLE::CNormalGuildBattleLogger *kLogger; // [sp+38h] [bp-20h]@5
  GUILD_BATTLE::CNormalGuildBattleLogger *v13; // [sp+40h] [bp-18h]@5
  GUILD_BATTLE::CNormalGuildBattleLogger *v14; // [sp+48h] [bp-10h]@7
  GUILD_BATTLE::CNormalGuildBattle *v15; // [sp+60h] [bp+8h]@1
  int na; // [sp+68h] [bp+10h]@1
  unsigned int v17; // [sp+70h] [bp+18h]@1
  unsigned int dwSerial; // [sp+78h] [bp+20h]@1

  dwSerial = dwCharacSerial;
  v17 = dwGuildSerial;
  na = n;
  v15 = this;
  v4 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwGuildSerial == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v15->m_k1P) )
  {
    v13 = &v15->m_kLogger;
    v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v15->m_k2P);
    kLogger = v13;
    pkField = v15->m_pkField;
    uiID = v15->m_dwID;
    wszDestGuild = v6;
    GUILD_BATTLE::CNormalGuildBattleGuild::LogIn(
      &v15->m_k1P,
      na,
      dwSerial,
      v15->m_byGuildBattleNumber,
      v6,
      uiID,
      pkField,
      v13);
  }
  else if ( v17 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v15->m_k2P) )
  {
    v14 = &v15->m_kLogger;
    v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v15->m_k1P);
    kLogger = v14;
    pkField = v15->m_pkField;
    uiID = v15->m_dwID;
    wszDestGuild = v7;
    GUILD_BATTLE::CNormalGuildBattleGuild::LogIn(
      &v15->m_k2P,
      na,
      dwSerial,
      v15->m_byGuildBattleNumber,
      v7,
      uiID,
      pkField,
      v14);
  }
}
