/*
 * Function: ?Set@_AuthKeyTicket@MiningTicket@@QEAAXGEEEE@Z
 * Address: 0x1400A6BA0
 */

void __fastcall MiningTicket::_AuthKeyTicket::Set(MiningTicket::_AuthKeyTicket *this, unsigned __int16 byYear, char by<PERSON><PERSON><PERSON>, char byDay, char byHour, char byNumofTime)
{
  this->uiData = ((byYear & 0x3FFF) << 18) | this->uiData & 0x3FFFF;
  this->uiData = ((byMonth & 0xF) << 14) | this->uiData & 0xFFFC3FFF;
  this->uiData = ((byDay & 0x1F) << 9) | this->uiData & 0xFFFFC1FF;
  this->uiData = 16 * (byHour & 0x1F) | this->uiData & 0xFFFFFE0F;
  this->uiData = byNumofTime & 0xF | this->uiData & 0xFFFFFFF0;
}
