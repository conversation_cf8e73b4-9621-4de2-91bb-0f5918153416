@echo off
echo Testing NexusPro Build Environment...

REM Try to find Visual Studio 2022
set "VS_PATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise" (
    set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise"
)

if "%VS_PATH%"=="" (
    echo ERROR: Visual Studio 2022 not found!
    pause
    exit /b 1
)

echo Found Visual Studio at: %VS_PATH%

REM Set up build environment
call "%VS_PATH%\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo Testing MSBuild...
where msbuild

echo.
echo Attempting to build NexusPro.Core...
cd NexusPro
msbuild NexusPro.Core\NexusPro.Core.vcxproj /p:Configuration=Debug /p:Platform=x64 /v:minimal

echo.
echo Build test completed.
pause
