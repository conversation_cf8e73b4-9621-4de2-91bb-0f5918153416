﻿/*
 * Function: j_??$?0$$CBHPEAVCAsyncLogInfo@@@?$pair@HPEAVCAsyncLogInfo@@@std@@@AEBU?$pair@$$CBHPEAVCAsyncLogInfo@@@1@@Z
 * Address: 0x140013B1F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  std::pair<int,CAsyncLogInfo *>::pair<int,CAsyncLogInfo *>(std::pair<int,CAsyncLogInfo *> *this, std::pair<int const ,CAsyncLogInfo *> *_Right)
{
  std::pair<int,CAsyncLogInfo *>::pair<int,CAsyncLogInfo *>(this, _Right);
}


