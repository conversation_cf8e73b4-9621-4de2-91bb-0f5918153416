﻿/*
 * Function: j_?OnRecvSession_ServerCheckSum_Request@HACKSHEILD_PARAM_ANTICP@@_NH@Z
 * Address: 0x14000C342
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(HACKSHEILD_PARAM_ANTICP *this, int nIndex)
{
  return HACKSHEILD_PARAM_ANTICP::OnRecvSession_ServerCheckSum_Request(this, nIndex);
}

