/*
 * Function: ?Update@CGuildBattleRankManager@GUILD_BATTLE@@QEAA_NEPEAE@Z
 * Address: 0x1403CA680
 */

char __fastcall GUILD_BATTLE::CGuildBattleRankManager::Update(GUILD_BATTLE::CGuildBattleRankManager *this, char byRace, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  unsigned __int16 v7; // [sp+20h] [bp-28h]@6
  unsigned __int16 v8; // [sp+24h] [bp-24h]@6
  int *v9; // [sp+28h] [bp-20h]@6
  char *v10; // [sp+30h] [bp-18h]@6
  unsigned __int16 j; // [sp+38h] [bp-10h]@6
  unsigned __int16 k; // [sp+3Ch] [bp-Ch]@11
  GUILD_BATTLE::CGuildBattleRankManager *v13; // [sp+50h] [bp+8h]@1
  char v14; // [sp+58h] [bp+10h]@1

  v14 = byRace;
  v13 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    v7 = 0;
    v8 = 0;
    v9 = 0i64;
    v10 = pLoadData;
    GUILD_BATTLE::CGuildBattleRankManager::Clear(v13, byRace);
    ++v13->m_dwVer[(unsigned __int8)v14];
    for ( j = 0; j < (signed int)*(_WORD *)v10; ++j )
    {
      if ( (signed int)v7 >= 10 )
      {
        v13->m_ppkList[(unsigned __int8)v14][v8].dwCurVer = v13->m_dwVer[(unsigned __int8)v14];
        v13->m_ppkList[(unsigned __int8)v14][v8].byCnt = 10;
        v7 = 0;
        ++v8;
      }
      v9 = &v13->m_ppkList[(unsigned __int8)v14][v8].list[v7].nRank;
      *v9 = *(_DWORD *)&v10[44 * j + 4];
      *((_BYTE *)v9 + 4) = v10[44 * j + 8];
      strcpy_0((char *)v9 + 5, &v10[44 * j + 9]);
      *(int *)((char *)v9 + 22) = *(_DWORD *)&v10[44 * j + 28];
      *(int *)((char *)v9 + 26) = *(_DWORD *)&v10[44 * j + 32];
      *(int *)((char *)v9 + 30) = *(_DWORD *)&v10[44 * j + 36];
      *(int *)((char *)v9 + 34) = *(_DWORD *)&v10[44 * j + 40];
      v13->m_dwGuildSerial[(unsigned __int8)v14][v8][v7++] = *(_DWORD *)&v10[44 * j + 44];
    }
    v13->m_ppkList[(unsigned __int8)v14][v8].byCnt = v7;
    for ( k = 0; k <= (signed int)v8; ++k )
    {
      v13->m_ppkList[(unsigned __int8)v14][k].dwCurVer = v13->m_dwVer[(unsigned __int8)v14];
      v13->m_ppkList[(unsigned __int8)v14][k].byMaxPage = v8 + 1;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
