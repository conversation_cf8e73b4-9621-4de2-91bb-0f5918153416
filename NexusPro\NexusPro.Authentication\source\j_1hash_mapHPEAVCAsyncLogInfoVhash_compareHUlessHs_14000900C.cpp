/*
 * Function: j_??1?$hash_map@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@@stdext@@QEAA@XZ
 * Address: 0x14000900C
 */

void __fastcall stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::~hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>(stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this)
{
  stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::~hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>(this);
}
