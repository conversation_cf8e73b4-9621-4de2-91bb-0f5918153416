﻿  0CMoneySupplyMgrQEAAXZ_14042B630.cpp
  0_economy_history_dataQEAAXZ_140205870.cpp
  0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp
  1CMoneySupplyMgrUEAAXZ_14042B660.cpp
  BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp
  BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp
  CanAddMoneyForMaxLimGoldYA_N_K0Z_14003F190.cpp
  CanAddMoneyForMaxLimMoneyYA_N_K0Z_14003F110.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(6,17): error C2653: '_economy_history_data': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(6,17): error C2653: 'CWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(6,40): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(6,23): error C2182: 'BeginModalState': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(6,40): error C2182: '_economy_history_data': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(6,34): error C2182: 'CMoneySupplyMgr': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(6,39): error C2065: 'CWnd': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(6,85): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(6,67): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(6,45): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(8,3): error C2653: 'CWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(8,9): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(11,25): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(8,3): error C2059: syntax error: 'this'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(11,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(6,17): error C2653: 'CFrameWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(6,17): error C2653: '_guild_money_io_download_zocl': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(6,35): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(8,9): error C2374: 'BeginModalState': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(6,23):
      see declaration of 'BeginModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(6,28): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(6,48): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(8,58): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(6,35): error C2182: '{dtor}': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(6,28): error C2182: 'BeginModalState': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(6,48): error C2182: '_guild_money_io_download_zocl': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(11,3): error C2086: 'int _economy_history_data': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(6,40):
      see declaration of '_economy_history_data'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(6,51): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(8,9): error C2086: 'int BeginModalState': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(6,23):
      see declaration of 'BeginModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(8,66): error C2001: newline in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(6,44): error C2065: 'CFrameWnd': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(6,109): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(11,26): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(6,68): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(8,25): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(6,55): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(8,41): error C2825: 'CMoneySupplyMgr': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(13,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(8,59): error C2510: 'CMoneySupplyMgr': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(8,3): error C2059: syntax error: 'this'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(13,3): error C2040: 'v4': 'int' differs in levels of indirection from 'int *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(8,3): error C2059: syntax error: 'this'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(8,3): error C2653: 'CFrameWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(13,8): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(8,58): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(8,14): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(14,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(8,66): error C2001: newline in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(8,14): error C2374: 'BeginModalState': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(6,28):
      see declaration of 'BeginModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(14,3): error C2040: 'v1': 'int' differs in levels of indirection from '__int64 *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(8,41): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(8,14): error C2086: 'int BeginModalState': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(6,28):
      see declaration of 'BeginModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(15,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(8,30): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(15,17): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(15,19): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(15,19): error C2371: 'i': redefinition; different basic types
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(9,18):
      see declaration of 'i'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(15,22): error C2059: syntax error: '--'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(15,26): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(16,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(16,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(20,3): error C2825: '_economy_history_data': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(20,26): error C2510: '_economy_history_data': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(20,26): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(20,30): error C2440: 'initializing': cannot convert from 'int *' to 'int'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(20,30):
      There is no context in which this conversion is possible
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(21,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\0_economy_history_dataQEAAXZ_140205870.cpp(21,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CanAddMoneyForMaxLimMoneyYA_N_K0Z_14003F110.cpp(18,7): error C2065: '_DWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CanAddMoneyForMaxLimMoneyYA_N_K0Z_14003F110.cpp(18,15): error C2059: syntax error: ')'
  CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CanAddMoneyForMaxLimGoldYA_N_K0Z_14003F190.cpp(18,7): error C2065: '_DWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CanAddMoneyForMaxLimGoldYA_N_K0Z_14003F190.cpp(18,15): error C2059: syntax error: ')'
  check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp
  ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp
  ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp
  DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp
  DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp
  DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp
  dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(6,17): error C2653: 'CGuildRanking': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(6,32): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(6,32): error C2182: 'CheckMaxGuildMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(6,51): error C2065: 'CGuildRanking': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(6,66): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(6,72): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(6,16): error C2653: 'CPropertySheet': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(6,32): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(6,46): error C2065: 'CPropertySheet': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(6,62): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(8,10): error C2653: 'CPropertySheet': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp(6,17): error C2653: 'CMainThread': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp(6,56): error C2065: 'CMainThread': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp(6,69): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp(6,75): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(6,75): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(6,58): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(6,75): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(6,83): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(6,16): error C2653: 'CWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(6,22): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(6,36): error C2065: 'CWnd': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(6,42): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(8,10): error C2653: 'CWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(6,17): error C2653: 'CHonorGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(6,30): error C2182: 'DQSCompleteInAtradTaxMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(6,57): error C2065: 'CHonorGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(6,76): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(6,20): error C2653: 'CPropertySheet': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(6,36): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(6,44): error C2065: 'CPropertySheet': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(6,60): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(8,10): error C2653: 'CPropertySheet': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(6,20): error C2653: 'CDialog': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(6,37): error C2065: 'CDialog': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(6,46): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(8,10): error C2653: 'CDialog': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp(9,1): error C2143: syntax error: missing ';' before '}'
  dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp
  dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp
  dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(6,75): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(6,58): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(6,75): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(6,83): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp
  dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp
  dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp
  dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp
  dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(6,70): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(6,56): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(6,70): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(6,78): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(6,92): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(6,67): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(6,92): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(6,100): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(6,16): error C2018: unknown character '0x60'
  dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(6,92): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(6,67): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(6,92): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(6,100): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp
  dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(6,93): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(6,68): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(6,68): error C2182: '{dtor}': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(6,93): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(6,101): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(6,92): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(6,67): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(6,92): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(6,100): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(6,92): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(6,67): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(6,92): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(6,100): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(6,66): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(6,66): error C2182: 'AddRunnableContext': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(6,83): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(6,65): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(6,83): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(6,93): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(6,68): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(6,68): error C2182: '{dtor}': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(6,93): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(6,101): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp
  dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp
  dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp
  dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp
  dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp
  dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(6,83): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(6,65): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(6,83): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(6,83): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(6,65): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(6,83): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(6,66): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(6,66): error C2182: 'AddRunnableContext': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(6,83): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(6,65): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(6,83): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(6,83): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(6,65): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(6,83): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(6,83): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(6,65): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(6,83): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(6,83): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(6,65): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(6,83): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp
  dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp
  dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp
  dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp
  dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(6,75): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(6,61): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(6,61): error C2182: 'AddToRunnables': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(6,75): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(6,83): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(6,75): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(6,61): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(6,61): error C2182: 'AddToRunnables': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(6,75): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(6,83): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp
  dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp
  dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(6,75): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(6,61): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(6,61): error C2182: 'AddToRunnables': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(6,75): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(6,83): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(6,74): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(6,74): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(6,82): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(6,77): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(6,56): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(6,56): error C2182: 'AllocateScheduleGroup': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(6,77): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(6,85): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,23): error C2039: 'money_get': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,32): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,23): error C2182: 'money_get': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,32): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,43): error C2039: 'istreambuf_iterator': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,73): error C2039: 'char_traits': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,94): error C2039: 'do_get': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(6,108): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,23): error C2039: 'money_get': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,32): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,23): error C2182: 'money_get': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,32): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,43): error C2039: 'istreambuf_iterator': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,73): error C2039: 'char_traits': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,94): error C2039: 'do_get': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(6,108): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,23): error C2039: 'money_get': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,32): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,23): error C2182: 'money_get': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,32): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,43): error C2039: 'istreambuf_iterator': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,73): error C2039: 'char_traits': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,94): error C2039: 'do_get': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(6,108): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(6,85): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(6,55): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(6,55): error C2182: 'FoundAvailableVirtualProcessor': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(6,85): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(6,93): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp
  dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(6,85): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(6,55): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(6,55): error C2182: 'FoundAvailableVirtualProcessor': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(6,85): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(6,93): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp
  dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp
  dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp
  dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(6,88): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(6,59): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(6,88): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(6,96): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(6,88): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(6,59): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(6,88): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(6,96): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(6,84): error C2143: syntax error: missing ';' before 'constant'
  dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(6,54): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(6,84): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(6,55): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(6,38): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(6,38): error C2182: 'wait_for_multiple': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(6,55): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(6,63): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp(6,5): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp(6,6): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp(6,38): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp(6,38): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp(6,46): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(6,89): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(6,60): error C2182: 'MoveCompletionListToRunnables': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(6,89): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(6,97): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(6,89): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(6,60): error C2182: 'MoveCompletionListToRunnables': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(6,50): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(6,89): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(6,38): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(6,97): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(6,38): error C2182: 'wait_for_one': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(6,50): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(6,58): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(6,89): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(6,60): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(6,60): error C2182: 'MoveCompletionListToRunnables': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(6,89): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(6,97): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406502F0.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0CB0.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0FF0.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A11E0.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1520.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A16E0.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1A20.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A5D40.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406502F0.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A11E0.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0CB0.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0FF0.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1520.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A5D40.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1A20.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A16E0.cpp(1,1): error C1041: cannot open program database 'D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\bin\Debug\NexusPro.Economy.pdb'; if multiple CL.EXE write to the same .PDB file, please use /FS
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp
  dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,52): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,31): error C2182: 'task_completion_event': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,52): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,69): error C2039: '_Cancel': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,82): error C2039: 'shared_ptr': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,93): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(6,141): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,52): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,31): error C2182: 'task_completion_event': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,52): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,69): error C2039: '_Cancel': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,82): error C2039: 'shared_ptr': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,93): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(6,141): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,51): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,51): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,68): error C2039: '_Cancel': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,81): error C2039: 'shared_ptr': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,92): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(6,140): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,16): error C2018: unknown character '0x60'
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,43): error C2059: syntax error: '<'
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,51): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,51): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,68): error C2039: '_Cancel': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,81): error C2039: 'shared_ptr': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,92): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(6,140): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,42): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,42): error C2059: syntax error: '<'
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,49): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,77): error C2039: '_Reset': is not a member of '`global namespace''
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(6,91): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,51): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,51): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,68): error C2039: '_Cancel': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,81): error C2039: 'shared_ptr': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,92): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(6,140): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(6,77): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(6,56): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(6,56): error C2182: 'AllocateScheduleGroup': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(6,77): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(6,85): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,23): error C2039: 'money_get': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,32): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,23): error C2182: 'money_get': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,32): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,43): error C2039: 'istreambuf_iterator': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,73): error C2039: 'char_traits': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,94): error C2039: 'do_get': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(6,108): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,23): error C2039: 'money_get': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,32): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,23): error C2182: 'money_get': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,32): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,43): error C2039: 'istreambuf_iterator': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,73): error C2039: 'char_traits': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,94): error C2039: 'do_get': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(6,108): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
  dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp
  dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(6,55): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(6,38): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(6,38): error C2182: 'wait_for_multiple': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(6,55): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(6,63): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp(6,5): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp(6,6): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp(6,38): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp(6,38): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp(6,46): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(6,92): error C2039: 'dtor$0': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,23): error C2039: 'money_get': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,32): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,23): error C2182: 'money_get': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,32): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,43): error C2039: 'istreambuf_iterator': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,73): error C2039: 'char_traits': is not a member of 'std'
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\predefined C++ types (compiler internal)(357,11):
      see declaration of 'std'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,94): error C2039: 'do_get': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(6,108): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp
  dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp(6,5): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp(6,6): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp(6,38): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp(6,38): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp(6,46): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,16): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,17): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,42): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,42): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,49): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,77): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(6,91): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(6,50): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(6,38): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(6,38): error C2182: 'wait_for_one': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(6,50): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(6,58): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp
  dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp
  dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp
  dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp
  dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp
  dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(6,92): error C2039: 'dtor$2': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(6,92): error C2039: 'dtor$2': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp
  eAddDalantYAXHHZ_1402A41B0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(6,92): error C2039: 'dtor$2': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(6,92): error C2039: 'dtor$2': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(6,92): error C2039: 'dtor$2': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  eGetDalantYANHZ_1402A4390.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eAddDalantYAXHHZ_1402A41B0.cpp(8,3): error C2065: 'e_EconomySystem': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eAddDalantYAXHHZ_1402A41B0.cpp(8,50): error C2065: 'e_EconomySystem': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eAddDalantYAXHHZ_1402A41B0.cpp(9,8): error C2065: 'e_EconomySystem': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eAddDalantYAXHHZ_1402A41B0.cpp(10,7): error C2065: '_QWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eAddDalantYAXHHZ_1402A41B0.cpp(10,15): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(6,92): error C2039: 'dtor$2': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp
  eGetOldDalantYANHZ_1402A47A0.cpp
  EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp(6,23): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp(6,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp(6,32): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp(7,1): error C2059: syntax error: '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(6,17): error C2653: 'CWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(6,23): error C2182: 'EndModalLoop': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(6,36): error C2065: 'CWnd': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(6,42): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(6,48): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,17): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,18): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,43): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,31): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,31): error C2182: '_Greedy_node': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,43): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,50): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,78): error C2039: '_Reset': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(6,92): error C2039: 'dtor$1': is not a member of '`global namespace''
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetDalantYANHZ_1402A4390.cpp(17,7): error C2065: '_DWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetDalantYANHZ_1402A4390.cpp(17,15): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetDalantYANHZ_1402A4390.cpp(20,10): error C2065: 'e_EconomySystem': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetDalantYANHZ_1402A4390.cpp(20,50): error C2065: 'e_EconomySystem': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetOldDalantYANHZ_1402A47A0.cpp(17,7): error C2065: '_DWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetOldDalantYANHZ_1402A47A0.cpp(17,15): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\eGetOldDalantYANHZ_1402A47A0.cpp(20,10): error C2065: 'e_EconomySystem': undeclared identifier
  EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp
  EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(6,17): error C2653: 'CWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(6,23): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(6,17): error C2653: 'CFrameWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(6,23): error C2182: 'EndModalState': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(6,28): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(6,37): error C2065: 'CWnd': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(6,28): error C2182: 'EndModalState': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(6,43): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(6,42): error C2065: 'CFrameWnd': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(6,53): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(8,3): error C2653: 'CWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(8,9): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(8,3): error C2653: 'CFrameWnd': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(8,9): error C2374: 'EndModalState': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(6,23):
      see declaration of 'EndModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(8,14): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(8,14): error C2374: 'EndModalState': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(6,28):
      see declaration of 'EndModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(8,14): error C2086: 'int EndModalState': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(6,28):
      see declaration of 'EndModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(8,28): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(8,9): error C2086: 'int EndModalState': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(6,23):
      see declaration of 'EndModalState'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(8,23): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp(9,1): error C2143: syntax error: missing ';' before '}'
  ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp
  ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp
  FindAllFileYAHPEADPEAPEADHZ_140480410.cpp
  GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp
  GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp
  GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp
  GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp
  InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(6,20): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(6,42): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(6,58): error C2065: 'GuildCreateEventInfo': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(6,80): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(6,20): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(6,42): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(6,62): error C2065: 'GuildCreateEventInfo': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(6,84): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp(6,51): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp(6,63): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp(6,69): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(6,17): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(6,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(6,26): error C2825: 'CMoneySupplyMgr': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(6,43): error C2510: 'CMoneySupplyMgr': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(6,43): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(7,1): error C2059: syntax error: '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp(6,58): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp(6,76): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp(6,58): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp(6,76): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(6,24): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(6,32): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(6,47): error C2065: 'CGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(6,55): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
  IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(11,10): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(11,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(11,11): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(13,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(13,3): error C2040: 'v5': 'int' differs in levels of indirection from 'int *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(13,8): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(14,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(14,3): error C2040: 'v1': 'int' differs in levels of indirection from '__int64 *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(15,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(15,17): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(15,19): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(15,19): error C2371: 'i': redefinition; different basic types
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(9,18):
      see declaration of 'i'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(15,22): error C2059: syntax error: '--'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(15,26): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(16,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(16,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(20,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(21,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp(21,1): error C2143: syntax error: missing ';' before '}'
  j_0CMoneySupplyMgrQEAAXZ_140004895.cpp
  j_0_economy_history_dataQEAAXZ_140009719.cpp
  j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp
  j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(6,25): error C2182: 'IOMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(6,33): error C2065: 'CGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(6,41): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(6,47): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(11,3): error C2065: '_WIN32_FIND_DATAA': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(11,21): error C2146: syntax error: missing ';' before identifier 'FindFileData'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(11,21): error C2065: 'FindFileData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(12,3): error C2065: 'HANDLE': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(12,10): error C2146: syntax error: missing ';' before identifier 'hFindFile'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(12,10): error C2065: 'hFindFile': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(6,34): error C2182: 'CMoneySupplyMgr': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(6,67): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(27,7): error C2065: '_DWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(27,15): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(30,33): error C2065: '_security_cookie': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(8,3): error C2825: 'CMoneySupplyMgr': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(31,3): error C2065: 'hFindFile': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(31,43): error C2065: 'FindFileData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(8,20): error C2510: 'CMoneySupplyMgr': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(31,15): error C3861: 'FindFirstFileA': identifier not found
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(8,20): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(8,20): error C2374: 'CMoneySupplyMgr': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(6,34):
      see declaration of 'CMoneySupplyMgr'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(8,20): error C2086: 'int CMoneySupplyMgr': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(6,34):
      see declaration of 'CMoneySupplyMgr'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(8,36): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(6,35): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(6,35): error C2182: '{dtor}': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(6,51): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(6,68): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(8,3): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(8,21): error C2825: '{dtor}': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(36,10): error C2065: 'hFindFile': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(8,21): error C2244: '{dtor}': unable to match function definition to an existing declaration
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(8,21):
      see declaration of '{dtor}'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(36,24): error C2065: 'HANDLE': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(8,37): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(40,38): error C2065: 'FindFileData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(40,7): error C3861: 'strcpy_s': identifier not found
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(44,24): error C2065: 'hFindFile': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(44,36): error C2065: 'FindFileData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(44,10): error C3861: 'FindNextFileA': identifier not found
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(46,13): error C2065: 'hFindFile': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp(46,3): error C3861: 'FindClose': identifier not found
  j_CanAddMoneyForMaxLimGoldYA_N_K0Z_140003C15.cpp
  j_CanAddMoneyForMaxLimMoneyYA_N_K0Z_140011FE5.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(6,17): error C2653: '_guild_money_io_download_zocl': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(6,48): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(6,48): error C2182: '_guild_money_io_download_zocl': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(6,109): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(8,3): error C2825: '_guild_money_io_download_zocl': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(8,34): error C2510: '_guild_money_io_download_zocl': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(8,34): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(8,34): error C2374: '_guild_money_io_download_zocl': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(6,48):
      see declaration of '_guild_money_io_download_zocl'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(8,34): error C2086: 'int _guild_money_io_download_zocl': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(6,48):
      see declaration of '_guild_money_io_download_zocl'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(8,64): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(6,17): error C2653: '_economy_history_data': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(6,40): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(6,40): error C2182: '_economy_history_data': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(6,85): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(8,3): error C2825: '_economy_history_data': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(8,26): error C2510: '_economy_history_data': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(8,26): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(8,26): error C2374: '_economy_history_data': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(6,40):
      see declaration of '_economy_history_data'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(8,26): error C2086: 'int _economy_history_data': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(6,40):
      see declaration of '_economy_history_data'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(8,48): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_0_economy_history_dataQEAAXZ_140009719.cpp(9,1): error C2143: syntax error: missing ';' before '}'
  j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp
  j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp(6,17): error C2653: 'CMainThread': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp(6,56): error C2065: 'CMainThread': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp(6,69): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp(6,75): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(6,17): error C2653: 'CGuildRanking': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(6,32): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(6,32): error C2182: 'CheckMaxGuildMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(6,51): error C2065: 'CGuildRanking': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(6,66): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(6,72): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp
  j_eAddDalantYAXHHZ_14000650A.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(6,17): error C2653: 'CHonorGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(6,30): error C2182: 'DQSCompleteInAtradTaxMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(6,57): error C2065: 'CHonorGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(6,76): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  j_eGetDalantYANHZ_140012CB5.cpp
  j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp
  j_eGetOldDalantYANHZ_14001212F.cpp
  j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eAddDalantYAXHHZ_14000650A.cpp(9,1): warning C4717: 'eAddDalant': recursive on all control paths, function will cause runtime stack overflow
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CanAddMoneyForMaxLimMoneyYA_N_K0Z_140011FE5.cpp(9,1): warning C4717: 'CanAddMoneyForMaxLimMoney': recursive on all control paths, function will cause runtime stack overflow
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_CanAddMoneyForMaxLimGoldYA_N_K0Z_140003C15.cpp(9,1): warning C4717: 'CanAddMoneyForMaxLimGold': recursive on all control paths, function will cause runtime stack overflow
  j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp(6,23): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp(6,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp(6,32): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp(7,1): error C2059: syntax error: '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  j_FindAllFileYAHPEADPEAPEADHZ_14000E787.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetDalantYANHZ_140012CB5.cpp(9,1): warning C4717: 'eGetDalant': recursive on all control paths, function will cause runtime stack overflow
  j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp
  j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp(6,58): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp(6,76): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_eGetOldDalantYANHZ_14001212F.cpp(9,1): warning C4717: 'eGetOldDalant': recursive on all control paths, function will cause runtime stack overflow
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp(6,58): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp(6,76): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(6,25): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(6,47): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(6,63): error C2065: 'GuildCreateEventInfo': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(6,85): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(8,10): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_FindAllFileYAHPEADPEAPEADHZ_14000E787.cpp(9,1): warning C4717: 'FindAllFile': recursive on all control paths, function will cause runtime stack overflow
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(6,25): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(6,47): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(6,67): error C2065: 'GuildCreateEventInfo': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(6,89): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(8,10): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(6,25): error C2182: 'GetTotalDalant': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(6,40): error C2065: 'CGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(6,48): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(8,3): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(8,11): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(8,11): error C2374: 'GetTotalDalant': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(6,25):
      see declaration of 'GetTotalDalant'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(8,11): error C2086: 'int GetTotalDalant': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(6,25):
      see declaration of 'GetTotalDalant'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(8,26): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp(9,1): error C2143: syntax error: missing ';' before '}'
  j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp
  j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp
  j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp
  j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp
  j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp
  j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp
  j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp
  j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(6,25): error C2182: 'IOMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(6,33): error C2065: 'CGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(6,41): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(6,47): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp(6,51): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp(6,63): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp(6,69): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(6,17): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(6,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(6,26): error C2825: 'CMoneySupplyMgr': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(6,43): error C2510: 'CMoneySupplyMgr': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(6,43): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(7,1): error C2059: syntax error: '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp(6,17): error C2653: 'CGuildRanking': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp(6,32): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp(6,53): error C2065: 'CGuildRanking': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp(6,68): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp(6,74): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp
  j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp
  j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp
  j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp(6,45): error C2065: 'CGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp(6,53): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp(6,59): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(6,34): error C2182: 'LoopMoneySupply': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(6,25): error C2182: 'PushHistory_IOMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(6,50): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(6,45): error C2065: 'CGuild': undeclared identifier
  j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(6,67): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(6,53): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(6,17): error C2653: 'CMgrGuildHistory': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(6,59): error C2062: type 'bool' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(6,35): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(8,3): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(6,17): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(6,17): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(6,17): error C2653: 'CMgrGuildHistory': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(6,35): error C2182: 'pop_money': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(8,20): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(6,27): error C2065: 'strFILE': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(6,39): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(6,35): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(6,45): error C2065: 'CMgrGuildHistory': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(8,20): error C2374: 'LoopMoneySupply': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(6,34):
      see declaration of 'LoopMoneySupply'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(6,36): error C2065: 'fstr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(6,39): error C2182: 'SetConsumeDalantFree': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(6,25): error C2182: 'SetGuild': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(6,35): error C2182: 'push_money': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(6,63): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(8,20): error C2086: 'int LoopMoneySupply': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(6,34):
      see declaration of 'LoopMoneySupply'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(6,42): error C2065: 'CDarkHoleDungeonQuestSetup': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(6,34): error C2065: 'CGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(6,60): error C2065: 'GuildCreateEventInfo': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(6,46): error C2065: 'CMgrGuildHistory': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(6,69): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(8,36): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(6,70): error C2065: 'pSetup': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(6,42): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(6,82): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(6,64): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(6,78): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(6,48): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(6,88): error C2062: type 'bool' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(6,70): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(6,17): error C2653: 'CHonorGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(6,30): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(6,30): error C2182: 'SetGuildMaintainMoney': this use of 'void' is not valid
  j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(6,52): error C2065: 'CHonorGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(6,65): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(6,71): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp
  j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp
  j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp
  j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp
  j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp
  j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(6,16): error C2653: '_qry_case_outputgmoney': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(6,40): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(6,45): error C2065: '_qry_case_outputgmoney': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(6,69): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(8,10): error C2653: '_qry_case_outputgmoney': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(6,17): error C2653: 'AutoMineMachine': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(6,34): error C2182: 'SubChargeCost': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(6,48): error C2065: 'AutoMineMachine': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(6,65): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(6,71): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(6,16): error C2653: '_guild_money_io_download_zocl': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(6,47): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(6,52): error C2065: '_guild_money_io_download_zocl': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(6,83): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(8,10): error C2653: '_guild_money_io_download_zocl': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(6,16): error C2653: '_qry_case_inputgmoney': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(6,39): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(6,44): error C2065: '_qry_case_inputgmoney': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(6,67): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(6,16): error C2653: '_MONEY_SUPPLY_DATA': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(8,10): error C2653: '_qry_case_inputgmoney': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(6,36): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(6,41): error C2065: '_MONEY_SUPPLY_DATA': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(6,61): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(8,10): error C2653: '_MONEY_SUPPLY_DATA': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp(6,49): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp(6,61): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp(6,67): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp(6,58): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp(6,76): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(6,16): error C2653: '_log_sheet_economy': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(6,36): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(6,41): error C2065: '_log_sheet_economy': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(6,61): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(8,10): error C2653: '_log_sheet_economy': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp(9,1): error C2143: syntax error: missing ';' before '}'
  j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp
  j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp
  j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp
  j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp
  j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp
  j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp
  j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp
  j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(6,34): error C2182: 'UpdateBuyUnitData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(6,52): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(6,69): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(6,75): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(6,34): error C2182: 'UpdateBuyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(6,48): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(6,65): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(6,71): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(6,34): error C2182: 'UpdateGateRewardMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(6,60): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(6,77): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(6,83): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(6,34): error C2182: 'UpdateSellData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(6,49): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(6,66): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(6,72): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(6,34): error C2182: 'UpdateHonorGuildMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(6,60): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(6,77): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(6,83): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(6,34): error C2182: 'UpdateQuestRewardMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(6,61): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(6,78): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(6,84): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(6,34): error C2182: 'UpdateUnitRepairingChargesData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(6,65): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(6,82): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(6,88): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(6,34): error C2182: 'UpdateFeeMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(6,53): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
  j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(6,76): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(7,1): error C2143: syntax error: missing ';' before '{'
  j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp
  j__ReadEconomyIniFileYA_NXZ_140007F31.cpp
  j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp
  j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp
  LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,35): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(6,17): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,18): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(6,17): error C2182: '_UpdateNewEconomy': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,36): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(6,35): error C2065: '_economy_calc_data': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,43): error C2146: syntax error: missing ';' before identifier 'deleting'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(6,55): error C2065: 'pData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,62): error C2001: newline in constant
  LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,62): error C2015: too many characters in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,62): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(8,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,52): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(8,3): error C2374: '_UpdateNewEconomy': redefinition; multiple initialization
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(6,17):
      see declaration of '_UpdateNewEconomy'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(6,62): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(8,3): error C2086: 'int _UpdateNewEconomy': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(6,17):
      see declaration of '_UpdateNewEconomy'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(8,21): error C2065: 'pData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,35): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(8,27): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(8,54): error C2001: newline in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,18): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp(9,1): error C2143: syntax error: missing ';' before '}'
  ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp(8,54): error C2015: too many characters in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,36): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,52): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,43): error C2146: syntax error: missing ';' before identifier 'deleting'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,17): error C2653: 'CGuildRanking': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,62): error C2001: newline in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,32): error C2146: syntax error: missing ';' before identifier 'LoadGuildMoneyIOInfo'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(6,34): error C2182: 'LoopMoneySupply': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,62): error C2015: too many characters in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,53): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(6,50): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,62): error C2143: syntax error: missing ';' before 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,77): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(6,67): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,52): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,111): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(6,62): error C2059: syntax error: 'constant'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,143): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(14,19): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,168): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(14,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(6,192): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(8,27): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(14,20): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp(6,17): error C2653: 'CQuestMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(8,54): error C2001: newline in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(16,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp(6,28): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp(8,54): error C2015: too many characters in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(16,3): error C2040: 'v7': 'int' differs in levels of indirection from 'int *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp(6,47): error C2065: 'CQuestMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(16,8): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp(6,58): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(17,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp(6,64): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(17,3): error C2040: 'v1': 'int' differs in levels of indirection from '__int64 *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(18,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(18,18): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(18,20): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(18,20): error C2371: 'i': redefinition; different basic types
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(9,18):
      see declaration of 'i'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(18,23): error C2059: syntax error: '--'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(18,27): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(19,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(19,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(23,3): error C2059: syntax error: 'if'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(24,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(24,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(37,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp(37,1): error C2143: syntax error: missing ';' before '}'
  pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,44): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,25): error C2146: syntax error: missing ';' before identifier 'ManagePopGuildMoney'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,45): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,62): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,89): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,118): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,145): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(6,162): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\j__ReadEconomyIniFileYA_NXZ_140007F31.cpp(9,1): warning C4717: '_ReadEconomyIniFile': recursive on all control paths, function will cause runtime stack overflow
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(6,17): error C2653: 'CMgrGuildHistory': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(6,35): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(6,35): error C2182: 'pop_money': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(6,45): error C2065: 'CMgrGuildHistory': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(6,63): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(6,69): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(6,25): error C2182: 'PushHistory_IOMoney': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(6,45): error C2065: 'CGuild': undeclared identifier
  push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp
  qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp
  SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(6,53): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(6,59): error C2062: type 'bool' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp
  SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp
  size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp
  size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(6,17): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(6,27): error C2065: 'strFILE': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(6,36): error C2065: 'fstr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(6,42): error C2065: 'CDarkHoleDungeonQuestSetup': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(6,70): error C2065: 'pSetup': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(6,78): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(6,27): error C2653: '_log_sheet_economy': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(6,47): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(6,52): error C2065: '_log_sheet_economy': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,17): error C2653: 'CHonorGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(6,72): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,6): error C2182: '__usercall': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,30): error C2146: syntax error: missing ';' before identifier 'SetGuildMaintainMoney'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,64): error C2143: syntax error: missing ')' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,64): error C2143: syntax error: missing ';' before '*'
  size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp
  size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(6,17): error C2653: 'CGuild': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(6,17): error C2653: 'CMgrGuildHistory': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(6,27): error C2653: '_guild_money_io_download_zocl': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(6,35): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(6,25): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(6,58): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(6,35): error C2182: 'push_money': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(6,25): error C2182: 'SetGuild': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(6,63): error C2065: '_guild_money_io_download_zocl': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(6,46): error C2065: 'CMgrGuildHistory': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(6,34): error C2065: 'CGuild': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(6,64): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(6,94): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(6,42): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(6,70): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(6,48): error C2062: type 'unsigned int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(8,3): error C2059: syntax error: 'if'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(10,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(11,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp(11,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,52): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,65): error C2059: syntax error: 'this'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,69): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,88): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,113): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,142): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,159): error C2018: unknown character '0x40'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(6,166): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(6,27): error C2653: '_MONEY_SUPPLY_DATA': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(6,47): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(6,52): error C2065: '_MONEY_SUPPLY_DATA': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(6,72): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp(9,1): error C2143: syntax error: missing ';' before '}'
  size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp
  SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp
  TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp
  TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp
  UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(6,17): error C2653: 'GuildCreateEventInfo': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(6,39): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(6,39): error C2182: 'SetConsumeDalantFree': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(6,60): error C2065: 'GuildCreateEventInfo': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(6,82): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(6,88): error C2062: type 'bool' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(6,27): error C2653: '_qry_case_outputgmoney': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(6,51): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(6,56): error C2065: '_qry_case_outputgmoney': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(6,80): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp(6,58): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp(6,76): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(6,27): error C2653: '_qry_case_inputgmoney': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(6,50): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(6,55): error C2065: '_qry_case_inputgmoney': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(6,78): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(8,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(9,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp(9,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(6,17): error C2653: 'AutoMineMachine': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(6,34): error C2182: 'SubChargeCost': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp(6,17): error C2653: 'CNetworkEX': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(6,48): error C2065: 'AutoMineMachine': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp(6,29): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(6,65): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp(6,49): error C2065: 'CNetworkEX': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(6,71): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp(6,61): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp(6,67): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp
  UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(6,34): error C2182: 'UpdateBuyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(6,48): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(6,65): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(6,71): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(6,34): error C2182: 'UpdateBuyUnitData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(6,52): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(6,69): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(6,75): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(6,34): error C2182: 'UpdateGateRewardMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(6,60): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(6,77): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(6,83): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(6,34): error C2182: 'UpdateFeeMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(6,53): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(6,70): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(6,76): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(6,34): error C2182: 'UpdateHonorGuildMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(6,60): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(6,77): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(6,83): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(6,34): error C2182: 'UpdateQuestRewardMoneyData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(6,61): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(6,78): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(6,84): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(6,34): error C2182: 'UpdateUnitRepairingChargesData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(6,65): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(6,82): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(6,88): error C2062: type 'int' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
  _CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.cpp(6,17): error C2653: 'CMainThread': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.cpp(8,3): error C2653: 'CCheckSumGuildData': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.cpp(8,44): error C2065: 'CCheckSumGuildData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.cpp(8,64): error C2059: syntax error: ')'
  _CMoneySupplyMgrInstance__1_dtor0_140095100.cpp
  _ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp
  _FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_CMoneySupplyMgrInstance__1_dtor0_140095100.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
  _ReadEconomyIniFileYA_NXZ_1402A5040.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,1): error C2653: 'CryptoPP': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,19): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,11): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,17): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,46): error C2018: unknown character '0x60'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,29): error C2825: 'CMoneySupplyMgr': must be a class or namespace when followed by '::'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,47): error C2510: 'CMoneySupplyMgr': left of '::' must be a class/struct/union
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,47): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,31): error C2653: 'Concurrency': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,47): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,74): error C2653: 'CryptoPP': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,54): error C2146: syntax error: missing ';' before identifier 'deleting'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,64): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,73): error C2001: newline in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,64): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(6,73): error C2015: too many characters in constant
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,93): error C2065: 'a1': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,97): error C2059: syntax error: 'const'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(6,110): error C2653: 'CryptoPP': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(16,7): error C2065: '_DWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(16,15): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(19,11): error C2065: 'EqSukList': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(19,3): error C3861: 'LODWORD': identifier not found
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(19,38): error C3861: 'GetPrivateProfileIntA': identifier not found
  _UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp
  __CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp
  __HrLoadAllImportsForDll_14067693C.cpp
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(24,16): error C2065: 'EqSukList': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(6,17): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(6,17): error C2182: '_UpdateNewEconomy': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(6,35): error C2065: '_economy_calc_data': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(6,55): error C2065: 'pData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(29,22): error C2143: syntax error: missing ';' before '*'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(29,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(29,23): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(31,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(31,3): error C2040: 'v22': 'int' differs in levels of indirection from 'int *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(31,9): error C2065: 'pData': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(32,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(32,3): error C2040: 'v1': 'int' differs in levels of indirection from '__int64 *'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(33,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(33,18): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(33,20): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(10,3): error C2065: 'ImgDelayDescr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(33,20): error C2371: 'i': redefinition; different basic types
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(9,18):
      see declaration of 'i'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(10,18): error C2065: 'i': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(33,23): error C2059: syntax error: '--'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(33,27): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(27,10): error C2065: '_DWORD': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(34,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(27,18): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(34,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(28,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,22): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,34): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,24): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp(6,17): error C2653: 'CQuestMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,24): error C2086: 'int nRaceCode': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(21,7):
      see declaration of 'nRaceCode'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp(6,28): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp(6,47): error C2065: 'CQuestMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,34): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp(6,58): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,39): error C2059: syntax error: '++'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(38,51): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(29,11): error C2065: 'i': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(39,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp(6,64): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(29,16): error C2065: 'ImgDelayDescr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(39,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(29,31): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(31,13): error C2065: 'i': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,22): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(31,15): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,34): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(33,13): error C2065: 'i': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,24): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(33,26): error C2059: syntax error: ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,24): error C2086: 'int nRaceCode': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(21,7):
      see declaration of 'nRaceCode'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(36,21): error C2065: '_ImageBase': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(37,21): error C2065: '_ImageBase': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,34): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,39): error C2059: syntax error: '++'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(45,51): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(62,10): error C2065: 'i': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(46,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(65,47): error C2065: '_ImageBase': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(46,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(54,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(65,60): error C2065: 'i': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(54,3): error C2371: 'v15': redefinition; different basic types
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(22,9):
      see declaration of 'v15'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(55,3): error C2059: syntax error: 'if'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,22): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,34): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,24): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(71,27): error C2065: 'i': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(71,9): error C3861: '_delayLoadHelper2': identifier not found
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,24): error C2086: 'int nRaceCode': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(21,7):
      see declaration of 'nRaceCode'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(77,3): error C2059: syntax error: 'return'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(78,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,34): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\__HrLoadAllImportsForDll_14067693C.cpp(78,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,39): error C2059: syntax error: '++'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(57,51): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(58,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(58,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,3): error C2059: syntax error: 'for'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,22): error C2143: syntax error: missing ')' before ';'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,34): error C2143: syntax error: missing ';' before '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,24): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,24): error C2086: 'int nRaceCode': redefinition
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(21,7):
      see declaration of 'nRaceCode'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(24,8): error C3861: 'LODWORD': identifier not found
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,34): error C2059: syntax error: '<'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,39): error C2059: syntax error: '++'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(63,51): error C2059: syntax error: ')'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(64,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(64,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(81,3): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(81,3): error C2371: 'v21': redefinition; different basic types
      D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(28,9):
      see declaration of 'v21'
  
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(82,3): error C2059: syntax error: 'if'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(83,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(83,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(87,3): error C2059: syntax error: 'else'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(88,3): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(88,3): error C2447: '{': missing function header (old-style formal list?)
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(97,1): error C2059: syntax error: '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp(97,1): error C2143: syntax error: missing ';' before '}'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp(30,5): error C3861: 'MyMessageBox': identifier not found
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(6,17): error C2653: 'CMoneySupplyMgr': is not a class or namespace name
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(6,34): warning C4229: anachronism used: modifiers on data are ignored
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(6,34): error C2182: 'UpdateSellData': this use of 'void' is not valid
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(6,49): error C2065: 'CMoneySupplyMgr': undeclared identifier
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(6,66): error C2355: 'this': can only be referenced inside non-static member functions or non-static data member initializers
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(6,72): error C2062: type 'char' unexpected
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(7,1): error C2143: syntax error: missing ';' before '{'
D:\_1.NexusPro_Project2025_RFOnlineGameguard\NexusPro\NexusPro.Economy\source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp(7,1): error C2447: '{': missing function header (old-style formal list?)
