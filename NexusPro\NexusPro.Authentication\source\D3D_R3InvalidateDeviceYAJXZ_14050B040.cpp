﻿/*
 * Function: ?D3D_R3InvalidateDevice@@YAJXZ
 * Address: 0x14050B040
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64 D3D_R3InvalidateDevice(void)
{
  CN_InvalidateNature();
  ReleaseVertexShaderList();
  ReleaseBlurVBuffer();
  ReleaseFullScreenEffect();
  if ( stOldRenderTarget )
  {
    ((void (*)(void))stOldRenderTarget->vfptr->Release)();
    stOldRenderTarget = 0LL;
  }
  if ( stOldStencilZ )
  {
    ((void (*)(void))stOldStencilZ->vfptr->Release)();
    stOldStencilZ = 0LL;
  }
  return 0LL;
}

