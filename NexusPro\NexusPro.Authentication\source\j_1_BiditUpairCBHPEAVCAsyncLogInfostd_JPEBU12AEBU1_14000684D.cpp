﻿/*
 * Function: j_??1?$_Bidit@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@_JPEBU12@AEBU12@@std@@@XZ
 * Address: 0x14000684D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>::~_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>(std::_Bidit<std::pair<int const ,CAsyncLogInfo *>,__int64,std::pair<int const ,CAsyncLogInfo *> const *,std::pair<int const ,CAsyncLogInfo *> const &> *this)
{
  std::_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>::~_Bidit<std::pair<int const,CAsyncLogInfo *>,__int64,std::pair<int const,CAsyncLogInfo *> const *,std::pair<int const,CAsyncLogInfo *> const &>(this);
}

