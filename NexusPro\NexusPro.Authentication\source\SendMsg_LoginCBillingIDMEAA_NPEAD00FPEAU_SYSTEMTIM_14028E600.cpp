/*
 * Function: ?SendMsg_Login@CBillingID@@MEAA_NPEAD00FPEAU_SYSTEMTIME@@J@Z
 * Address: 0x14028E600
 */

char __fastcall CBillingID::SendMsg_Login(CBillingID *this, char *szID, char *szIP, char *szCMS, __int16 iType, _SYSTEMTIME *pstEndDate, int lRemainTime)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v10; // [sp+0h] [bp-C8h]@1
  char Dst; // [sp+38h] [bp-90h]@8
  char v12; // [sp+45h] [bp-83h]@8
  char v13; // [sp+55h] [bp-73h]@9
  __int16 v14; // [sp+5Ch] [bp-6Ch]@8
  int v15; // [sp+5Eh] [bp-6Ah]@8
  char v16; // [sp+62h] [bp-66h]@11
  char pbyType; // [sp+94h] [bp-34h]@12
  char v18; // [sp+95h] [bp-33h]@12
  unsigned __int64 v19; // [sp+B0h] [bp-18h]@4
  CBillingID *v20; // [sp+D0h] [bp+8h]@1
  char *v21; // [sp+E0h] [bp+18h]@1
  char *v22; // [sp+E8h] [bp+20h]@1

  v22 = szCMS;
  v21 = szIP;
  v20 = this;
  v7 = &v10;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v19 = (unsigned __int64)&v10 ^ _security_cookie;
  if ( v20->m_bOper )
  {
    if ( iType <= 100 )
    {
      v14 = iType;
      v15 = lRemainTime;
      memcpy_0(&Dst, szID, 0xDui64);
      memcpy_0(&v12, v21, 0x10ui64);
      if ( v22 )
        memcpy_0(&v13, v22, 7ui64);
      if ( pstEndDate )
        memcpy_0(&v16, pstEndDate, 0x10ui64);
      pbyType = 29;
      v18 = 4;
      CNetProcess::LoadSendMsg(qword_1414F20A0, 0, &pbyType, &Dst, 0x3Au);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
