﻿/*
 * Function: _std::vector_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0_____::erase_::_1_::dtor$1
 * Address: 0x1403C5180
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::vector_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0_____::erase_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>>::~_Vector_iterator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>>(*(std::_Vector_iterator<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0>,std::allocator<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> > > **)(a2 + 96));
}

