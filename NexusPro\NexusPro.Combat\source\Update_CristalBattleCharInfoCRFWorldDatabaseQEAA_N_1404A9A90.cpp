/*
 * Function: ?Update_CristalBattleCharInfo@CRFWorldDatabase@@QEAA_NKEEHGG@Z
 * Address: 0x1404A9A90
 */

bool __fastcall CRFWorldDatabase::Update_CristalBattleCharInfo(CRFWorldDatabase *this, unsigned int dwSerial, char byHSKTime, char byPvpGrade, int iPvpPoint, unsigned __int16 wKillPoint, unsigned __int16 wDiePoint)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v10; // [sp+0h] [bp-218h]@1
  int v11; // [sp+20h] [bp-1F8h]@9
  int v12; // [sp+28h] [bp-1F0h]@9
  int v13; // [sp+30h] [bp-1E8h]@9
  int v14; // [sp+38h] [bp-1E0h]@9
  int v15; // [sp+40h] [bp-1D8h]@9
  int v16; // [sp+48h] [bp-1D0h]@9
  int v17; // [sp+50h] [bp-1C8h]@9
  int v18; // [sp+58h] [bp-1C0h]@9
  unsigned int v19; // [sp+60h] [bp-1B8h]@9
  char Dst; // [sp+80h] [bp-198h]@6
  char v21; // [sp+81h] [bp-197h]@6
  unsigned int v22; // [sp+198h] [bp-80h]@6
  int v23; // [sp+19Ch] [bp-7Ch]@6
  int v24; // [sp+1A0h] [bp-78h]@6
  unsigned __int16 v25; // [sp+1C4h] [bp-54h]@6
  unsigned __int16 v26; // [sp+1C6h] [bp-52h]@6
  unsigned __int16 v27; // [sp+1C8h] [bp-50h]@6
  unsigned __int16 v28; // [sp+1E4h] [bp-34h]@6
  unsigned __int16 v29; // [sp+1E6h] [bp-32h]@6
  unsigned __int16 v30; // [sp+1E8h] [bp-30h]@6
  int j; // [sp+1F4h] [bp-24h]@6
  unsigned __int64 v32; // [sp+200h] [bp-18h]@4
  CRFWorldDatabase *v33; // [sp+220h] [bp+8h]@1
  unsigned int v34; // [sp+228h] [bp+10h]@1
  char v35; // [sp+238h] [bp+20h]@1

  v35 = byPvpGrade;
  v34 = dwSerial;
  v33 = this;
  v7 = &v10;
  for ( i = 132i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v32 = (unsigned __int64)&v10 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byHSKTime < 3 )
  {
    Dst = 0;
    memset(&v21, 0, 0xFFui64);
    v22 = 0;
    v23 = 0;
    v24 = 0;
    v25 = 0;
    v26 = 0;
    v27 = 0;
    v28 = 0;
    v29 = 0;
    v30 = 0;
    for ( j = 0; j < 3; ++j )
    {
      *(&v22 + j) = 0;
      *(&v25 + j) = 0;
      *(&v28 + j) = 0;
    }
    *(&v22 + (unsigned __int8)byHSKTime) = iPvpPoint;
    *(&v25 + (unsigned __int8)byHSKTime) = wKillPoint;
    *(&v28 + (unsigned __int8)byHSKTime) = wDiePoint;
    memset_0(&Dst, 0, 0x100ui64);
    v19 = v34;
    v18 = v30;
    v17 = v29;
    v16 = v28;
    v15 = (unsigned __int8)v35;
    v14 = v27;
    v13 = v24;
    v12 = v26;
    v11 = v23;
    sprintf(
      &Dst,
      "Update tbl_general set Pvp_0=%d, Pk_0=%d, Pvp_1=%d, Pk_1=%d, Pvp_2=%d, Pk_2=%d, CharacterGrade=%d, Die_0=%d, Die_1"
      "=%d, Die_2=%d where serial=%d",
      v22,
      v25);
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v33->vfptr, &Dst, 1);
  }
  else
  {
    result = 0;
  }
  return result;
}
