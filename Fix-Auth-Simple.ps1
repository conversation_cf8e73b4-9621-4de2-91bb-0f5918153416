Write-Host "NexusPro Authentication Module Fix" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

$sourceDir = "NexusPro\NexusPro.Authentication\source"
$includeText = @"
#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

"@

# Check if source directory exists
if (!(Test-Path $sourceDir)) {
    Write-Host "Source directory not found: $sourceDir" -ForegroundColor Red
    exit 1
}

# Get all source files
$sourceFiles = Get-ChildItem $sourceDir -Filter "*.cpp"
Write-Host "Found $($sourceFiles.Count) source files" -ForegroundColor White

$batchSize = 20
$totalBatches = [Math]::Ceiling($sourceFiles.Count / $batchSize)
$processedFiles = 0
$modifiedFiles = 0

Write-Host "Processing in $totalBatches batches of $batchSize files each" -ForegroundColor Cyan
Write-Host ""

for ($batch = 0; $batch -lt $totalBatches; $batch++) {
    $startIndex = $batch * $batchSize
    $endIndex = [Math]::Min($startIndex + $batchSize - 1, $sourceFiles.Count - 1)
    $currentBatch = $sourceFiles[$startIndex..$endIndex]
    
    Write-Host "Batch $($batch + 1)/$totalBatches (files $($startIndex + 1)-$($endIndex + 1)):" -ForegroundColor Yellow
    
    $batchModified = 0
    
    foreach ($file in $currentBatch) {
        $fileName = $file.Name
        if ($fileName.Length -gt 50) {
            $fileName = $fileName.Substring(0, 47) + "..."
        }
        Write-Host "  Processing: $fileName" -ForegroundColor Gray
        
        try {
            $content = Get-Content $file.FullName -Raw -ErrorAction Stop
            $originalContent = $content
            $changed = $false
            
            # Add includes if missing
            if ($content -notmatch "#include.*NexusProCommon\.h") {
                $lines = Get-Content $file.FullName
                $insertIndex = 0
                
                # Find insertion point
                for ($i = 0; $i -lt $lines.Count; $i++) {
                    $line = $lines[$i].Trim()
                    if ($line -and $line -ne "*/" -and !$line.StartsWith("/*") -and !$line.StartsWith("//") -and !$line.StartsWith("*")) {
                        $insertIndex = $i
                        break
                    }
                }
                
                # Insert includes
                $newLines = $lines[0..($insertIndex-1)] + $includeText.Split("`n") + $lines[$insertIndex..($lines.Count-1)]
                $newLines | Set-Content $file.FullName -Encoding UTF8
                $changed = $true
                Write-Host "    Added includes" -ForegroundColor Green
            }
            
            # Apply basic fixes
            $content = Get-Content $file.FullName -Raw
            
            # Fix _DWORD -> DWORD
            if ($content -match "_DWORD") {
                $content = $content -replace "_DWORD", "DWORD"
                $changed = $true
            }
            
            # Fix hex constants
            if ($content -match "-858993460") {
                $content = $content -replace "-858993460", "0xCCCCCCCC"
                $changed = $true
            }
            
            # Fix i64 literals
            if ($content -match "(\d+)i64") {
                $content = $content -replace "(\d+)i64", '$1LL'
                $changed = $true
            }
            
            # Fix calling conventions
            $conventions = @("QEAA", "UEAA", "MEAA", "AEAA", "YEAA", "PEAA")
            foreach ($conv in $conventions) {
                if ($content -match $conv) {
                    $content = $content -replace $conv, ""
                    $changed = $true
                }
            }
            
            # Remove __fastcall
            if ($content -match "__fastcall") {
                $content = $content -replace "__fastcall", ""
                $changed = $true
            }
            
            # Save changes
            if ($changed) {
                $content | Set-Content $file.FullName -Encoding UTF8
                $batchModified++
                Write-Host "    Applied fixes" -ForegroundColor Green
            } else {
                Write-Host "    No changes needed" -ForegroundColor Gray
            }
            
            $processedFiles++
            
        } catch {
            Write-Host "    Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    $modifiedFiles += $batchModified
    Write-Host "  Batch result: $batchModified/$($currentBatch.Count) files modified" -ForegroundColor White
    
    # Progress
    $progress = ($batch + 1) / $totalBatches * 100
    Write-Host "  Progress: $($progress.ToString('F1'))%" -ForegroundColor Cyan
    Write-Host ""
    
    # Test build every 10 batches
    if (($batch + 1) % 10 -eq 0 -or $batch -eq $totalBatches - 1) {
        if ($batchModified -gt 0) {
            Write-Host "Testing build after batch $($batch + 1)..." -ForegroundColor Yellow
            
            $projectPath = "NexusPro\NexusPro.Authentication\NexusPro.Authentication.vcxproj"
            $msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
            
            if (Test-Path $msbuildPath) {
                try {
                    $buildResult = & $msbuildPath $projectPath /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo 2>&1
                    
                    if ($LASTEXITCODE -eq 0) {
                        Write-Host "  Build successful!" -ForegroundColor Green
                    } else {
                        Write-Host "  Build failed - continuing anyway" -ForegroundColor Yellow
                    }
                } catch {
                    Write-Host "  Build test error - continuing" -ForegroundColor Yellow
                }
            }
            Write-Host ""
        }
    }
}

Write-Host "=" * 50 -ForegroundColor Green
Write-Host "AUTHENTICATION MODULE COMPLETION" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green
Write-Host "Files processed: $processedFiles" -ForegroundColor White
Write-Host "Files modified: $modifiedFiles" -ForegroundColor White
Write-Host "Success rate: $(($modifiedFiles/$processedFiles*100).ToString('F1'))%" -ForegroundColor White

# Final build test
Write-Host ""
Write-Host "Final build test..." -ForegroundColor Yellow
$projectPath = "NexusPro\NexusPro.Authentication\NexusPro.Authentication.vcxproj"
$msbuildPath = "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"

if (Test-Path $msbuildPath) {
    try {
        $finalResult = & $msbuildPath $projectPath /p:Configuration=Debug /p:Platform=x64 /v:minimal /nologo 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "FINAL BUILD SUCCESS!" -ForegroundColor Green
        } else {
            Write-Host "Final build has errors:" -ForegroundColor Red
            $finalResult | Select-Object -Last 20 | ForEach-Object { 
                if ($_.ToString().Trim()) {
                    Write-Host "  $_" -ForegroundColor Red
                }
            }
        }
    } catch {
        Write-Host "Final build test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "MSBuild not found at: $msbuildPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "Authentication module processing completed!" -ForegroundColor Green
