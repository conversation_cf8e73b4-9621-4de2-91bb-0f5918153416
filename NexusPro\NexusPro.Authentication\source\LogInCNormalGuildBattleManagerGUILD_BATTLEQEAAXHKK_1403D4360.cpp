/*
 * Function: ?LogIn@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXHKK@Z
 * Address: 0x1403D4360
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::LogIn(GUILD_BATTLE::CNormalGuildBattleManager *this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle *v7; // [sp+20h] [bp-18h]@5
  GUILD_BATTLE::CNormalGuildBattleManager *v8; // [sp+40h] [bp+8h]@1
  int na; // [sp+48h] [bp+10h]@1
  unsigned int dwGuildSeriala; // [sp+50h] [bp+18h]@1
  unsigned int dwCharacSeriala; // [sp+58h] [bp+20h]@1

  dwCharacSeriala = dwCharacSerial;
  dwGuildSeriala = dwGuildSerial;
  na = n;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwGuildSerial != -1 )
  {
    v7 = 0i64;
    v7 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v8, dwGuildSerial);
    if ( v7 )
    {
      if ( GUILD_BATTLE::CNormalGuildBattle::IsReadyOrCountState(v7) )
      {
        GUILD_BATTLE::CNormalGuildBattle::AskJoin(v7, na, dwGuildSeriala, dwCharacSeriala);
      }
      else if ( GUILD_BATTLE::CNormalGuildBattle::IsInBattle(v7) )
      {
        GUILD_BATTLE::CNormalGuildBattle::LogIn(v7, na, dwGuildSeriala, dwCharacSeriala);
      }
    }
  }
}
