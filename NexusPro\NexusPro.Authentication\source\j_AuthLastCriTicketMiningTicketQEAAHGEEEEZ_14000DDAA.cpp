﻿/*
 * Function: j_?AuthLastCriTicket@MiningTicket@@HGEEEE@Z
 * Address: 0x14000DDAA
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int __fastcall MiningTicket::AuthLastCriTicket(MiningTicket *this, unsigned __int16 byCurrentYear, char byCurrent<PERSON><PERSON><PERSON>, char byCurrentDay, char byCurrentHour, char byNumOfTime)
{
  return MiningTicket::AuthLastCriTicket(this, byCurrentYear, byCurrentMonth, byCurrentDay, byCurrentHour, byNumOfTime);
}

