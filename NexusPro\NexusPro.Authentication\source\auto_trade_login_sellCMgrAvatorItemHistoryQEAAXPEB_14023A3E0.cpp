/*
 * Function: ?auto_trade_login_sell@CMgrAvatorItemHistory@@QEAAXPEBDK0KPEAU_db_con@_STORAGE_LIST@@_JKKKKPEAD@Z
 * Address: 0x14023A3E0
 */

void __fastcall CMgrAvatorItemHistory::auto_trade_login_sell(CMgrAvatorItemHistory *this, const char *szBuyerName, unsigned int dwBuyerSerial, const char *szBuyerID, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pItem, __int64 tResultTime, unsigned int dwPrice, unsigned int dwTax, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  __int64 *v12; // rdi@1
  signed __int64 i; // rcx@1
  int v14; // edx@6
  int v15; // er8@6
  char *v16; // rax@7
  __int64 v17; // [sp+0h] [bp-B8h]@1
  unsigned __int64 v18; // [sp+20h] [bp-98h]@5
  char *v19; // [sp+28h] [bp-90h]@5
  unsigned __int64 v20; // [sp+30h] [bp-88h]@5
  unsigned int v21; // [sp+38h] [bp-80h]@5
  unsigned int v22; // [sp+40h] [bp-78h]@5
  unsigned int v23; // [sp+48h] [bp-70h]@5
  const char *v24; // [sp+50h] [bp-68h]@5
  char *v25; // [sp+58h] [bp-60h]@5
  void *v26; // [sp+60h] [bp-58h]@5
  unsigned int v27; // [sp+68h] [bp-50h]@6
  unsigned int v28; // [sp+70h] [bp-48h]@6
  unsigned int v29; // [sp+78h] [bp-40h]@6
  unsigned int v30; // [sp+80h] [bp-38h]@6
  char *v31; // [sp+88h] [bp-30h]@6
  char *v32; // [sp+90h] [bp-28h]@6
  tm *v33; // [sp+A0h] [bp-18h]@4
  _base_fld *v34; // [sp+A8h] [bp-10h]@7
  CMgrAvatorItemHistory *v35; // [sp+C0h] [bp+8h]@1
  const char *v36; // [sp+C8h] [bp+10h]@1
  unsigned int v37; // [sp+D0h] [bp+18h]@1
  const char *v38; // [sp+D8h] [bp+20h]@1

  v38 = szBuyerID;
  v37 = dwBuyerSerial;
  v36 = szBuyerName;
  v35 = this;
  v12 = &v17;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v12 = -*********;
    v12 = (__int64 *)((char *)v12 + 4);
  }
  v33 = localtime_5(&tResultTime);
  sBuf[0] = 0;
  if ( v33 )
  {
    v14 = v33->tm_mon + 1;
    v15 = v33->tm_year;
    v32 = v35->m_szCurTime;
    v31 = v35->m_szCurDate;
    v30 = dwLeftGold;
    v29 = dwLeftDalant;
    v28 = dwTax;
    v27 = dwPrice;
    v26 = (void *)v38;
    LODWORD(v25) = v37;
    v24 = v36;
    v23 = dwRegistSerial;
    v22 = v33->tm_sec;
    v21 = v33->tm_min;
    LODWORD(v20) = v33->tm_hour;
    LODWORD(v19) = v33->tm_mday;
    LODWORD(v18) = v14;
    sprintf_s(
      sBuf,
      0x2800ui64,
      "AUTO TRADE(SELL): login sell selldate(%04d-%02d-%02d %02d:%02d:%02d) reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u)"
      " $D:%u $G:%u [%s %s]\r\n",
      (unsigned int)(v15 + 1900));
  }
  else
  {
    v26 = v35->m_szCurTime;
    v25 = v35->m_szCurDate;
    LODWORD(v24) = dwLeftGold;
    v23 = dwLeftDalant;
    v22 = dwTax;
    v21 = dwPrice;
    v20 = (unsigned __int64)v38;
    LODWORD(v19) = v37;
    v18 = (unsigned __int64)v36;
    sprintf_s(
      sBuf,
      0x2800ui64,
      "AUTO TRADE(SELL): login sell selldate(invalid) reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u) $D:%u $G:%u [%s %s]\r\n",
      dwRegistSerial);
  }
  strcat_s(sData, 0x4E20ui64, sBuf);
  v34 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v16 = DisplayItemUpgInfo(pItem->m_byTableCode, pItem->m_dwLv);
  v20 = pItem->m_lnUID;
  v19 = v16;
  v18 = pItem->m_dwDur;
  sprintf_s(sBuf, 0x2800ui64, "\t- %s_%u_@%s[%I64u]\r\n", v34->m_strCode);
  strcat_s(sData, 0x4E20ui64, sBuf);
}
