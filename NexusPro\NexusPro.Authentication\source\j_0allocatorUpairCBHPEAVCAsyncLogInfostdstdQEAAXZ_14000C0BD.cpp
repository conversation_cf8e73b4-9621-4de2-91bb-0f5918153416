﻿/*
 * Function: j_??0?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@@XZ
 * Address: 0x14000C0BD
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(std::allocator<std::pair<int const ,CAsyncLogInfo *> > *this)
{
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(this);
}

