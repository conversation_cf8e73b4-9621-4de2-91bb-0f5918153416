﻿/*
 * Function: j_??$_Construct@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@U12@@std@@YAXPEAU?$pair@$$CBHPEAVCAsyncLogInfo@@@0@AEBU10@@Z
 * Address: 0x14000F3A3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::_Construct<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(std::pair<int const ,CAsyncLogInfo *> *_Ptr, std::pair<int const ,CAsyncLogInfo *> *_Val)
{
  std::_Construct<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(_Ptr, _Val);
}

