/*
 * Function: ??1?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140464F50
 */

void __fastcall CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::~MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>(CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>,CryptoPP::HMAC<CryptoPP::SHA1>>::~AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>,CryptoPP::HMAC<CryptoPP::SHA1>>((CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1> >,CryptoPP::HMAC<CryptoPP::SHA1> > *)&v4->vfptr);
}
