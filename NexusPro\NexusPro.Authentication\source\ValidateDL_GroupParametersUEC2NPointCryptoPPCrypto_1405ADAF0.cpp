/*
 * Function: ?Validate@?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1405ADAF0
 */

char __fastcall CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::Validate(__int64 a1, __int64 a2, unsigned int a3)
{
  int (__fastcall ***v3)(_QWORD); // rax@1
  char result; // al@2
  __int64 v5; // rax@6
  __int64 v6; // ST48_8@6
  __int64 v7; // rax@6
  bool v8; // [sp+58h] [bp-10h]@7
  int v9; // [sp+5Ch] [bp-Ch]@10
  __int64 v10; // [sp+70h] [bp+8h]@1
  __int64 v11; // [sp+78h] [bp+10h]@1
  unsigned int v12; // [sp+80h] [bp+18h]@1

  v12 = a3;
  v11 = a2;
  v10 = a1;
  LODWORD(v3) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(a1 - 32) + 48i64))(a1 - 32);
  if ( (unsigned __int8)(**v3)(v3) )
  {
    if ( *(_DWORD *)(v10 - 16) <= v12 )
    {
      v8 = (unsigned __int8)(*(int (__fastcall **)(signed __int64, __int64, _QWORD))(*(_QWORD *)(v10 - 32) + 128i64))(
                              v10 - 32,
                              v11,
                              v12)
        && (LODWORD(v5) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(v10 - 32) + 48i64))(v10 - 32),
            v6 = v5,
            LODWORD(v7) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(v10 - 32) + 8i64))(v10 - 32),
            (unsigned __int8)(*(int (__fastcall **)(signed __int64, _QWORD, __int64, __int64))(*(_QWORD *)(v10 - 32)
                                                                                             + 136i64))(
                               v10 - 32,
                               v12,
                               v7,
                               v6));
      if ( v8 )
        v9 = v12 + 1;
      else
        v9 = 0;
      *(_DWORD *)(v10 - 16) = v9;
      result = v8;
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
