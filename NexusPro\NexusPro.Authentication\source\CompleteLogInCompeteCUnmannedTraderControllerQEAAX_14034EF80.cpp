﻿/*
 * Function: ?CompleteLogInCompete@CUnmannedTraderController@@XPEAD@Z
 * Address: 0x14034EF80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CUnmannedTraderController::CompleteLogInCompete(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@5
  unsigned int v5; // ecx@5
  int v6; // ecx@11
  __int64 v7; // [sp+0h] [bp-58h]@1
  int v8; // [sp+20h] [bp-38h]@5
  int v9; // [sp+28h] [bp-30h]@11
  int v10; // [sp+30h] [bp-28h]@11
  char *v11; // [sp+40h] [bp-18h]@4
  unsigned int j; // [sp+48h] [bp-10h]@5
  CUnmannedTraderController *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v2 = &v7;
  for ( i = 20LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = pData;
  if ( !pData[8] )
  {
    v4 = *(_WORD *)v11;
    v5 = (unsigned __int8)v11[9];
    v8 = *((DWORD *)v11 + 1);
    CUnmannedTraderController::Log(
      v13,
      "CUnmannedTraderController::CompleteLogInCompete( BYTE byRet, char * pLoadData )\r\n"
      "\t\tType(%u) wInx(%u) dwSeller(%u)\r\n",
      v5,
      v4);
    for ( j = 0; (signed int)j < *((_WORD *)v11 + 5); ++j )
    {
      if ( (unsigned __int8)v11[16 * j + 13] != 255 )
      {
        if ( v11[16 * j + 12] )
        {
          v6 = (unsigned __int8)v11[16 * j + 24];
          v10 = (unsigned __int8)v11[16 * j + 13];
          v9 = v6;
          v8 = *(DWORD *)&v11[16 * j + 16];
          CUnmannedTraderController::Log(
            v13,
            "\t\t(%d)Nth Regist Serial(%u) dwBuyer(%u) UpdateState(%u) byProcUpdate(%u) DB Error!\r\n",
            j,
            *(DWORD *)&v11[16 * j + 20]);
        }
      }
    }
  }
}

