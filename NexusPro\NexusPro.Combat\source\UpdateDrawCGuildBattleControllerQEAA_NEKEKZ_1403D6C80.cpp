/*
 * Function: ?UpdateDraw@CGuildBattleController@@QEAA_NEKEK@Z
 * Address: 0x1403D6C80
 */

bool __fastcall CGuildBattleController::UpdateDraw(CGuildBattleController *this, char by1PRace, unsigned int dw1PGuildSerial, char by2PRace, unsigned int dw2PGuildSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleRankManager *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-38h]@1
  char v10; // [sp+48h] [bp+10h]@1
  unsigned int dw1PGuildSeriala; // [sp+50h] [bp+18h]@1
  char v12; // [sp+58h] [bp+20h]@1

  v12 = by2PRace;
  dw1PGuildSeriala = dw1PGuildSerial;
  v10 = by1PRace;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
  return GUILD_BATTLE::CGuildBattleRankManager::UpdateDraw(v7, v10, dw1PGuildSeriala, v12, dw2PGuildSerial) != 0;
}
