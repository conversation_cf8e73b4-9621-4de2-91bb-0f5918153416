/*
 * Function: ?WriteLogPer10Min_Combat@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x1402804E0
 */

void __fastcall CHolyStoneSystem::WriteLogPer10Min_Combat(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _class_fld *v3; // rax@11
  CPvpUserAndGuildRankingSystem *v4; // rax@16
  unsigned int v5; // eax@16
  int v6; // eax@17
  CPvpUserAndGuildRankingSystem *v7; // rax@18
  int v8; // eax@19
  __int64 v9; // r9@23
  __int64 v10; // rdx@28
  __int64 v11; // [sp+0h] [bp-998h]@1
  int v12; // [sp+20h] [bp-978h]@23
  int v13; // [sp+28h] [bp-970h]@23
  int v14; // [sp+30h] [bp-968h]@23
  int v15; // [sp+38h] [bp-960h]@23
  char Buffer; // [sp+50h] [bp-948h]@4
  int Dst; // [sp+E8h] [bp-8B0h]@4
  int v18; // [sp+ECh] [bp-8ACh]@23
  int v19; // [sp+F0h] [bp-8A8h]@23
  int v20[19]; // [sp+F4h] [bp-8A4h]@23
  int v21[501]; // [sp+140h] [bp-858h]@4
  int j; // [sp+914h] [bp-84h]@4
  CPlayer *v23; // [sp+918h] [bp-80h]@7
  int k; // [sp+920h] [bp-78h]@21
  int l; // [sp+924h] [bp-74h]@24
  int *v26; // [sp+930h] [bp-68h]@11
  int *v27; // [sp+938h] [bp-60h]@11
  int v28; // [sp+940h] [bp-58h]@16
  char *v29; // [sp+948h] [bp-50h]@17
  CLogFile *v30; // [sp+950h] [bp-48h]@17
  int v31; // [sp+958h] [bp-40h]@18
  char *v32; // [sp+960h] [bp-38h]@19
  CLogFile *v33; // [sp+968h] [bp-30h]@19
  CLogFile *v34; // [sp+970h] [bp-28h]@28
  unsigned __int64 v35; // [sp+978h] [bp-20h]@4
  CHolyStoneSystem *v36; // [sp+9A0h] [bp+8h]@1

  v36 = this;
  v1 = &v11;
  for ( i = 610i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v35 = (unsigned __int64)&v11 ^ _security_cookie;
  _strtime(&Buffer);
  CLogFile::Write(&v36->m_logPer10Min, "TIME : %s", &Buffer);
  memset_0(&Dst, 0, 0x30ui64);
  memset_0(v21, 0, 0x7D0ui64);
  for ( j = 0; j < 2532; ++j )
  {
    v23 = &g_Player + j;
    if ( v23->m_bLive
      && !v23->m_bCorpse
      && ((int (__fastcall *)(CPlayer *))v23->vfptr->GetLevel)(v23) >= 25
      && v23->m_pCurMap == g_Stone->m_pCurMap )
    {
      v26 = &Dst + 4 * CPlayerDB::GetRaceCode(&v23->m_Param);
      v3 = CPlayerDB::GetPtrCurClass(&v23->m_Param);
      v27 = &v26[v3->m_nClass];
      ++*v27;
      if ( !v23->m_Param.m_pGuild )
        goto LABEL_16;
      if ( v23->m_Param.m_pGuild->m_nIndex < 500 && v23->m_Param.m_pGuild->m_nIndex >= 0 )
      {
        ++v21[v23->m_Param.m_pGuild->m_nIndex];
LABEL_16:
        v28 = CPlayerDB::GetRaceCode(&v23->m_Param);
        v4 = CPvpUserAndGuildRankingSystem::Instance();
        v5 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v28, 0);
        if ( v23->m_dwObjSerial == v5 )
        {
          v29 = CPlayerDB::GetCharNameA(&v23->m_Param);
          v6 = CPlayerDB::GetRaceCode(&v23->m_Param);
          v30 = &v36->m_logPer10Min;
          CLogFile::Write(&v36->m_logPer10Min, "R.boss(%s): %s", szRaceCode_0[v6], v29);
        }
        else
        {
          v31 = CPlayerDB::GetRaceCode(&v23->m_Param);
          v7 = CPvpUserAndGuildRankingSystem::Instance();
          if ( CPvpUserAndGuildRankingSystem::IsRaceViceBoss(v7, v31, v23->m_dwObjSerial) )
          {
            v32 = CPlayerDB::GetCharNameA(&v23->m_Param);
            v8 = CPlayerDB::GetRaceCode(&v23->m_Param);
            v33 = &v36->m_logPer10Min;
            CLogFile::Write(&v36->m_logPer10Min, "vice-R.boss(%s): %s", szRaceCode_0[v8], v32);
          }
        }
        continue;
      }
    }
  }
  for ( k = 0; k < 3; ++k )
  {
    v9 = (unsigned int)(v20[4 * k] + *(&v19 + 4 * k) + *(&v18 + 4 * k) + *(&Dst + 4 * k));
    v15 = v20[4 * k];
    v14 = *(&v19 + 4 * k);
    v13 = *(&v18 + 4 * k);
    v12 = *(&Dst + 4 * k);
    CLogFile::Write(&v36->m_logPer10Min, "%s >> Total: %d Wr: %d Rr: %d Sp: %d Sc: %d", szRaceCode_0[k], v9);
  }
  for ( l = 0; l < 500; ++l )
  {
    if ( CGuild::IsFill(&g_Guild[l]) && v21[l] > 0 )
    {
      v10 = g_Guild[l].m_byRace;
      v34 = &v36->m_logPer10Min;
      v13 = g_Guild[l].m_nMemberNum;
      v12 = v21[l];
      CLogFile::Write(&v36->m_logPer10Min, "%s (%s) : %d / %d", g_Guild[l].m_aszName, szRaceCode_0[v10]);
    }
  }
  CLogFile::Write(&v36->m_logPer10Min, "============================================");
  CLogFile::Write(&v36->m_logPer10Min, "============================================");
}
