﻿/*
 * Function: j_?OnRecvSession_ClientCrc_Response@HACKSHEILD_PARAM_ANTICP@@_N_KPEAD@Z
 * Address: 0x14000338C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

bool __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCrc_Response(HACKSHEILD_PARAM_ANTICP *this, unsigned __int64 tSize, char *pMsg)
{
  return HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCrc_Response(this, tSize, pMsg);
}

