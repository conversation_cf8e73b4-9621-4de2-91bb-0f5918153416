/*
 * Function: ?ValidateParameters@EC2N@CryptoPP@@QEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x14062E210
 */

bool __fastcall CryptoPP::EC2N::ValidateParameters(CryptoPP::EC2N *this, struct CryptoPP::RandomNumberGenerator *a2, unsigned int a3)
{
  unsigned int v3; // ST28_4@2
  CryptoPP::GF2NP *v4; // rax@2
  unsigned int v5; // ST30_4@6
  CryptoPP::GF2NP *v6; // rax@6
  __int64 v7; // rax@11
  CryptoPP::PolynomialMod2 *v8; // rax@11
  bool v10; // [sp+20h] [bp-28h]@9
  bool v11; // [sp+2Ch] [bp-1Ch]@3
  bool v12; // [sp+34h] [bp-14h]@7
  bool v13; // [sp+38h] [bp-10h]@12
  CryptoPP::EC2N *v14; // [sp+50h] [bp+8h]@1
  unsigned int v15; // [sp+60h] [bp+18h]@1

  v15 = a3;
  v14 = this;
  v11 = !CryptoPP::PolynomialMod2::operator!(&this->m_b.reg)
     && (v3 = CryptoPP::PolynomialMod2::CoefficientCount(&v14->m_a),
         v4 = (CryptoPP::GF2NP *)CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->((__int64)&v14->m_field),
         v3 <= (unsigned int)CryptoPP::GF2NP::MaxElementBitLength(v4));
  v12 = v11
     && (v5 = CryptoPP::PolynomialMod2::CoefficientCount(&v14->m_b),
         v6 = (CryptoPP::GF2NP *)CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->((__int64)&v14->m_field),
         v5 <= (unsigned int)CryptoPP::GF2NP::MaxElementBitLength(v6));
  v10 = v12;
  if ( v15 >= 1 )
  {
    v13 = v12
       && (v7 = CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->((__int64)&v14->m_field),
           v8 = (CryptoPP::PolynomialMod2 *)CryptoPP::QuotientRing<CryptoPP::EuclideanDomainOf<CryptoPP::PolynomialMod2>>::GetModulus(v7),
           CryptoPP::PolynomialMod2::IsIrreducible(v8));
    v10 = v13;
  }
  return v10;
}
