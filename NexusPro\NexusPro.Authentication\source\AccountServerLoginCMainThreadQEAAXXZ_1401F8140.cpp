﻿/*
 * Function: ?AccountServerLogin@CMainThread@@XXZ
 * Address: 0x1401F8140
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  CMainThread::AccountServerLogin(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@7
  CNationSettingManager *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-178h]@1
  char Dest; // [sp+40h] [bp-138h]@4
  unsigned __int32 v7; // [sp+62h] [bp-116h]@5
  char Dst; // [sp+66h] [bp-112h]@7
  char ReturnedString; // [sp+B0h] [bp-C8h]@4
  char pbyType; // [sp+144h] [bp-34h]@7
  char v11; // [sp+145h] [bp-33h]@7
  unsigned __int64 v12; // [sp+160h] [bp-18h]@4
  CMainThread *v13; // [sp+180h] [bp+8h]@1

  v13 = this;
  v1 = &v5;
  for ( i = 92LL; i; --i )
  {
    *(DWORD *)v1 = 0xCCCCCCCC;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  strcpy_0(&Dest, v13->m_szWorldName);
  GetPrivateProfileStringA("System", "GateIP", "X", &ReturnedString, 0x80u, "..\\WorldInfo\\WorldInfo.ini");
  if ( !strcmp_0(&ReturnedString, "X") )
    v7 = GetIPAddress();
  else
    v7 = inet_addr(&ReturnedString);
  memcpy_s(&Dst, 0x20ui64, g_cbHashVerify, 0x20ui64);
  pbyType = 1;
  v11 = 1;
  v3 = _open_world_request_wrac::size((_open_world_request_wrac *)&Dest);
  CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &Dest, v3);
  v4 = CTSingleton<CNationSettingManager>::Instance();
  CNationSettingManager::SendCashDBDSNRequest(v4);
}


