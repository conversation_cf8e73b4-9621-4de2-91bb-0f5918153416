﻿/*
 * Function: ??$?0$$CBHPEAVCAsyncLogInfo@@@?$pair@HPEAVCAsyncLogInfo@@@std@@@AEBU?$pair@$$CBHPEAVCAsyncLogInfo@@@1@@Z
 * Address: 0x1403C7590
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  std::pair<int,CAsyncLogInfo *>::pair<int,CAsyncLogInfo *>(std::pair<int,CAsyncLogInfo *> *this, std::pair<int const ,CAsyncLogInfo *> *_Right)
{
  *this = (std::pair<int,CAsyncLogInfo *>)*_Right;
}

