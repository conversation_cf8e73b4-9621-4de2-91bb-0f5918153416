﻿/*
 * Function: ??$insert@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@XV?$_Iterator@$0A@@01@00@Z
 * Address: 0x1403C76B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::insert<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this, std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_Where, std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_First, std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_Last)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v6; // rax@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v7; // rax@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v8; // rax@4
  __int64 v9; // [sp+0h] [bp-D8h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> __formal; // [sp+30h] [bp-A8h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v11; // [sp+50h] [bp-88h]@4
  char v12; // [sp+58h] [bp-80h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v13; // [sp+70h] [bp-68h]@4
  char v14; // [sp+78h] [bp-60h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v15; // [sp+90h] [bp-48h]@4
  __int64 v16; // [sp+98h] [bp-40h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v17; // [sp+A0h] [bp-38h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v18; // [sp+A8h] [bp-30h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v19; // [sp+B0h] [bp-28h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v20; // [sp+B8h] [bp-20h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v21; // [sp+C0h] [bp-18h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *v22; // [sp+E0h] [bp+8h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v23; // [sp+E8h] [bp+10h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v24; // [sp+F0h] [bp+18h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *__that; // [sp+F8h] [bp+20h]@1

  __that = _Last;
  v24 = _First;
  v23 = _Where;
  v22 = this;
  v4 = &v9;
  for ( i = 52LL; i; --i )
  {
    *(DWORD *)v4 = 0xCCCCCCCC;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2LL;
  std::_Iter_cat<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(&__formal);
  v11 = (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)((char *)&__formal + 8);
  v13 = (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)&v12;
  v15 = (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)&v14;
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::_Iterator<0>(
    (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *)((char *)&__formal + 8),
    __that);
  v17 = v6;
  v18 = v6;
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::_Iterator<0>(
    v13,
    v24);
  v19 = v7;
  v20 = v7;
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::_Iterator<0>(
    v15,
    v23);
  v21 = v8;
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Insert<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(
    v22,
    v8,
    v20,
    v18,
    BYTE1(__formal._Mycont));
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::~_Iterator<0>(v23);
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::~_Iterator<0>(v24);
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::~_Iterator<0>(__that);
}


