﻿/*
 * Function: _std::vector_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0_____::_Xlen_::_1_::dtor$0
 * Address: 0x1403C6B80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int __fastcall std::vector_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0_____::_Xlen_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 104);
}

