﻿/*
 * Function: j_?Validate@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@NA@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1400018A2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

int CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate()
{
  return CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::Validate();
}

