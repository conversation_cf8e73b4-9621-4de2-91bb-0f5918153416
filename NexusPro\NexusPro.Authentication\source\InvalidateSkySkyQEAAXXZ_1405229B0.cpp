﻿/*
 * Function: ?InvalidateSky@Sky@@XXZ
 * Address: 0x1405229B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  Sky::InvalidateSky(Sky *this)
{
  Sky *v1; // rbx@1
  __int64 v2; // rcx@2
  __int64 v3; // rcx@4

  v1 = this;
  if ( *(float *)&dword_184A797B0 >= 1.0 )
  {
    v2 = *((_QWORD *)this + 5);
    if ( v2 )
    {
      (*(void (**)(void))(*(_QWORD *)v2 + 16LL))();
      *((_QWORD *)v1 + 5) = 0LL;
    }
    v3 = *((_QWORD *)v1 + 6);
    if ( v3 )
    {
      (*(void (**)(void))(*(_QWORD *)v3 + 16LL))();
      *((_QWORD *)v1 + 6) = 0LL;
    }
  }
}


