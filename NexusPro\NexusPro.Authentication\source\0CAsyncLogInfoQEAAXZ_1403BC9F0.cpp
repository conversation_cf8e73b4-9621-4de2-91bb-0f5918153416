/*
 * Function: CAsyncLogInfo Constructor
 * Address: 0x1403BC9F0
 * Decompiled from RF Online
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall CAsyncLogInfo::CAsyncLogInfo(CAsyncLogInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CAsyncLogInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_eType = -1;
  v4->m_dwLogCount = 0;
  v4->m_szLogDirPath = 0i64;
  v4->m_szLogFileName = 0i64;
  v4->m_szTypeName = 0i64;
  v4->m_pkTimer = 0i64;
  CNetCriticalSection::CNetCriticalSection(&v4->m_csLock);
}
