/*
 * Function: ?GenerateEphemeralKeyPair@AuthenticatedKeyAgreementDomain@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEAE1@Z
 * Address: 0x1405F6600
 */

void __fastcall CryptoPP::AuthenticatedKeyAgreementDomain::GenerateEphemeralKeyPair(CryptoPP::AuthenticatedKeyAgreementDomain *this, struct CryptoPP::RandomNumberGenerator *a2, unsigned __int8 *a3, unsigned __int8 *a4)
{
  CryptoPP::AuthenticatedKeyAgreementDomain *v4; // [sp+30h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v5; // [sp+38h] [bp+10h]@1
  unsigned __int8 *v6; // [sp+40h] [bp+18h]@1
  unsigned __int8 *v7; // [sp+48h] [bp+20h]@1

  v7 = a4;
  v6 = a3;
  v5 = a2;
  v4 = this;
  ((void (*)(void))this->vfptr[7].Clone)();
  ((void (__fastcall *)(CryptoPP::AuthenticatedKeyAgreementDomain *, struct CryptoPP::RandomNumberGenerator *, unsigned __int8 *, unsigned __int8 *))v4->vfptr[8].__vecDelDtor)(
    v4,
    v5,
    v6,
    v7);
}
