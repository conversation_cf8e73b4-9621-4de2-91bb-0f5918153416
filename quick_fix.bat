@echo off
echo NexusPro Quick Fix Script
echo ========================

echo Step 1: Checking Python...
python --version
if errorlevel 1 (
    echo Python not found! Please install Python 3.7+
    pause
    exit /b 1
)

echo.
echo Step 2: Generating headers...
python generate_headers.py
if errorlevel 1 (
    echo Header generation failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Checking MSBuild...
set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if not exist %MSBUILD% (
    set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD% (
    set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)

if not exist %MSBUILD% (
    echo MSBuild not found! Please install Visual Studio 2022
    pause
    exit /b 1
)

echo Found MSBuild: %MSBUILD%

echo.
echo Step 4: Building Core module first...
%MSBUILD% "NexusPro\NexusPro.Core\NexusPro.Core.vcxproj" /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal

echo.
echo Step 5: Building full solution...
%MSBUILD% "NexusPro\NexusPro.sln" /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal

echo.
echo Build process completed!
echo Check the output above for any errors.
pause
