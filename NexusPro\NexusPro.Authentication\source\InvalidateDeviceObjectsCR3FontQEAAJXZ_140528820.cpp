﻿/*
 * Function: ?InvalidateDeviceObjects@CR3Font@@JXZ
 * Address: 0x140528820
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

__int64  CR3Font::InvalidateDeviceObjects(CR3Font *this)
{
  CR3Font *v1; // rbx@1
  __int64 v2; // rcx@1

  v1 = this;
  v2 = *(_QWORD *)this;
  if ( v2 )
  {
    if ( *((DWORD *)v1 + 25) )
      (*(void (**)(void))(*(_QWORD *)v2 + 448LL))();
    if ( *((DWORD *)v1 + 26) )
      (*(void (**)(void))(**(_QWORD **)v1 + 448LL))();
  }
  *((DWORD *)v1 + 25) = 0;
  *((DWORD *)v1 + 26) = 0;
  CR3Font::PrivateRelease(v1);
  return 0LL;
}


