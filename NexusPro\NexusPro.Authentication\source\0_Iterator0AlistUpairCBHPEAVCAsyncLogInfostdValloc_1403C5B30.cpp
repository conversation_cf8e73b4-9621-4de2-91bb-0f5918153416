﻿/*
 * Function: ??0?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@PEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@@Z
 * Address: 0x1403C5B30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>::_Iterator<0>(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *this, std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *_Pnode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8LL; i; --i )
  {
    *(DWORD *)v2 = 0xCCCCCCCC;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Const_iterator<0>::_Const_iterator<0>(
    (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Const_iterator<0> *)&v5->_Mycont,
    _Pnode);
}

