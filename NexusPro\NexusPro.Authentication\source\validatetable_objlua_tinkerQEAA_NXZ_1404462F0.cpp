/*
 * Function: ?validate@table_obj@lua_tinker@@QEAA_NXZ
 * Address: 0x1404462F0
 */

bool __fastcall lua_tinker::table_obj::validate(lua_tinker::table_obj *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  const void *v3; // rax@5
  bool result; // al@6
  const void *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@7
  unsigned int j; // [sp+24h] [bp-14h]@7
  lua_tinker::table_obj *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9->m_pointer )
  {
    LODWORD(v3) = lua_topointer(v9->m_L, v9->m_index);
    if ( v9->m_pointer == v3 )
    {
      result = 1;
    }
    else
    {
      v7 = lua_gettop(v9->m_L);
      for ( j = 1; (signed int)j <= v7; ++j )
      {
        LODWORD(v5) = lua_topointer(v9->m_L, j);
        if ( v9->m_pointer == v5 )
        {
          v9->m_index = j;
          return 1;
        }
      }
      v9->m_pointer = 0i64;
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
