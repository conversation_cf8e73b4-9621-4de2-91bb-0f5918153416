﻿/*
 * Function: j_??$?0W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@AEBU?$pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@1@@Z
 * Address: 0x14000D4F4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

void __fastcall std::pair<int const,CAsyncLogInfo *>::pair<int const,CAsyncLogInfo *>(std::pair<int const ,CAsyncLogInfo *> *this, std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *_Right)
{
  std::pair<int const,CAsyncLogInfo *>::pair<int const,CAsyncLogInfo *>(this, _Right);
}

