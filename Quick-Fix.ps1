# NexusPro Quick Fix Script
Write-Host "NexusPro Quick Fix Script" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Step 1: Check Python
Write-Host "`nStep 1: Checking Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Python not found! Please install Python 3.7+" -ForegroundColor Red
    exit 1
}

# Step 2: Generate headers manually if Python script doesn't work
Write-Host "`nStep 2: Creating basic headers..." -ForegroundColor Yellow

$modules = Get-ChildItem -Path "NexusPro" -Directory | Where-Object { $_.Name -like "NexusPro.*" }

foreach ($module in $modules) {
    $sourcePath = Join-Path $module.FullName "source"
    $headerPath = Join-Path $module.FullName "headers"
    
    if (Test-Path $sourcePath) {
        if (-not (Test-Path $headerPath)) {
            New-Item -Path $headerPath -ItemType Directory -Force | Out-Null
        }
        
        $cppFiles = Get-ChildItem -Path $sourcePath -Filter "*.cpp"
        Write-Host "  Processing $($module.Name): $($cppFiles.Count) files"
        
        foreach ($cppFile in $cppFiles) {
            $headerFile = Join-Path $headerPath ($cppFile.BaseName + ".h")
            
            if (-not (Test-Path $headerFile)) {
                $headerContent = @"
#pragma once
#ifndef $($cppFile.BaseName.ToUpper())_H
#define $($cppFile.BaseName.ToUpper())_H

// Auto-generated header for $($cppFile.Name)
#include <windows.h>
#include <string>
#include <vector>
#include <memory>

// TODO: Add proper declarations

#endif // $($cppFile.BaseName.ToUpper())_H
"@
                Set-Content -Path $headerFile -Value $headerContent
            }
        }
    }
}

Write-Host "Header generation completed!" -ForegroundColor Green

# Step 3: Find MSBuild
Write-Host "`nStep 3: Finding MSBuild..." -ForegroundColor Yellow

$msbuildPaths = @(
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
    "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)

$msbuild = $null
foreach ($path in $msbuildPaths) {
    if (Test-Path $path) {
        $msbuild = $path
        break
    }
}

if (-not $msbuild) {
    Write-Host "MSBuild not found! Please install Visual Studio 2022" -ForegroundColor Red
    exit 1
}

Write-Host "Found MSBuild: $msbuild" -ForegroundColor Green

# Step 4: Try building Core module first
Write-Host "`nStep 4: Building Core module..." -ForegroundColor Yellow
$coreProject = "NexusPro\NexusPro.Core\NexusPro.Core.vcxproj"

if (Test-Path $coreProject) {
    & $msbuild $coreProject /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Core module built successfully!" -ForegroundColor Green
    } else {
        Write-Host "Core module build failed. Check errors above." -ForegroundColor Red
    }
} else {
    Write-Host "Core project file not found: $coreProject" -ForegroundColor Red
}

# Step 5: Try building full solution
Write-Host "`nStep 5: Building full solution..." -ForegroundColor Yellow
$solution = "NexusPro\NexusPro.sln"

if (Test-Path $solution) {
    & $msbuild $solution /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Solution built successfully!" -ForegroundColor Green
    } else {
        Write-Host "Solution build failed. This is expected - we need to fix errors step by step." -ForegroundColor Yellow
    }
} else {
    Write-Host "Solution file not found: $solution" -ForegroundColor Red
}

Write-Host "`nQuick fix process completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Review any build errors above" -ForegroundColor White
Write-Host "2. Run: python analyze_build_errors.py" -ForegroundColor White
Write-Host "3. Apply fixes from the generated reports" -ForegroundColor White
